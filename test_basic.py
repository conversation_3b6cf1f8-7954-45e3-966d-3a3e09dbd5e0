#!/usr/bin/env python3
"""
PyJianYin 基础功能测试脚本
用于验证项目的基本功能是否正常工作
"""

import sys
import os
from pathlib import Path
from rich.console import Console
from rich.panel import Panel

console = Console()

def test_imports():
    """测试模块导入"""
    console.print("🔍 [bold]测试模块导入...[/bold]")
    
    try:
        # 测试配置模块
        from config import get_config, setup_environment
        console.print("✅ config 模块导入成功")
        
        # 测试各个功能模块
        sys.path.append(str(Path(__file__).parent / 'asset_manager'))
        sys.path.append(str(Path(__file__).parent / 'storyboard_generator'))
        sys.path.append(str(Path(__file__).parent / 'draft_builder'))
        sys.path.append(str(Path(__file__).parent / 'export_controller'))
        
        from asset_analyzer import analyze_media
        console.print("✅ asset_analyzer 模块导入成功")
        
        from storyboard_generator import generate_storyboard
        console.print("✅ storyboard_generator 模块导入成功")
        
        from draft_builder import build_draft_from_storyboard
        console.print("✅ draft_builder 模块导入成功")
        
        from exporter import export_video
        console.print("✅ exporter 模块导入成功")
        
        return True
        
    except ImportError as e:
        console.print(f"❌ [red]模块导入失败: {e}[/red]")
        return False

def test_config():
    """测试配置功能"""
    console.print("\n🔧 [bold]测试配置功能...[/bold]")
    
    try:
        from config import get_config
        config = get_config()
        
        # 测试基本配置
        console.print(f"📁 项目根目录: {config.project_root}")
        console.print(f"📁 素材目录: {config.assets_dir}")
        console.print(f"🔑 API Key 状态: {'已配置' if config.validate_api_key() else '未配置'}")
        
        # 测试视频模式配置
        modes = list(config.video_modes.keys())
        console.print(f"🎬 支持的视频模式: {', '.join(modes)}")
        
        # 测试文件格式支持
        console.print(f"📹 支持的视频格式: {', '.join(config.supported_video_formats)}")
        console.print(f"🖼️ 支持的图片格式: {', '.join(config.supported_image_formats)}")
        
        return True
        
    except Exception as e:
        console.print(f"❌ [red]配置测试失败: {e}[/red]")
        return False

def test_environment():
    """测试环境设置"""
    console.print("\n🌍 [bold]测试环境设置...[/bold]")
    
    try:
        from config import setup_environment, get_config
        config = get_config()
        
        # 测试目录创建
        setup_environment()
        
        # 检查目录是否存在
        directories = [
            config.assets_dir,
            config.get_output_path(),
            config.get_examples_path(),
            config.assets_dir / "videos",
            config.assets_dir / "images",
            config.assets_dir / "music",
            config.assets_dir / "sound"
        ]
        
        for directory in directories:
            if directory.exists():
                console.print(f"✅ 目录存在: {directory}")
            else:
                console.print(f"❌ [red]目录不存在: {directory}[/red]")
                return False
        
        return True
        
    except Exception as e:
        console.print(f"❌ [red]环境测试失败: {e}[/red]")
        return False

def test_file_operations():
    """测试文件操作"""
    console.print("\n📄 [bold]测试文件操作...[/bold]")
    
    try:
        from config import get_config
        config = get_config()
        
        # 检查示例文件
        example_files = [
            config.get_examples_path() / "example.srt",
            config.get_examples_path() / "funny_example.srt",
            config.get_examples_path() / "economics_example.srt",
            config.get_examples_path() / "README.md"
        ]
        
        for file_path in example_files:
            if file_path.exists():
                console.print(f"✅ 示例文件存在: {file_path.name}")
            else:
                console.print(f"⚠️ [yellow]示例文件不存在: {file_path.name}[/yellow]")
        
        # 检查配置文件
        env_example = config.project_root / ".env.example"
        if env_example.exists():
            console.print("✅ .env.example 文件存在")
        else:
            console.print("❌ [red].env.example 文件不存在[/red]")
            return False
        
        return True
        
    except Exception as e:
        console.print(f"❌ [red]文件操作测试失败: {e}[/red]")
        return False

def test_command_line():
    """测试命令行界面"""
    console.print("\n💻 [bold]测试命令行界面...[/bold]")

    try:
        # 测试主程序导入
        import main
        console.print("✅ main.py 导入成功")

        # 测试 Click 命令
        from click.testing import CliRunner
        runner = CliRunner()

        # 测试帮助命令
        result = runner.invoke(main.cli, ['--help'])
        if result.exit_code == 0:
            console.print("✅ --help 命令正常")
        else:
            console.print(f"❌ [red]--help 命令失败: {result.output}[/red]")
            return False

        # 测试 status 命令
        result = runner.invoke(main.cli, ['status'])
        if result.exit_code == 0:
            console.print("✅ status 命令正常")
        else:
            console.print(f"⚠️ [yellow]status 命令警告: {result.exit_code}[/yellow]")

        return True

    except Exception as e:
        console.print(f"❌ [red]命令行测试失败: {e}[/red]")
        return False

def test_api_tools():
    """测试 API 相关工具"""
    console.print("\n🧪 [bold]测试 API 工具...[/bold]")

    try:
        # 检查 API 测试脚本是否存在
        api_test_script = Path("test_gemini_api.py")
        if api_test_script.exists():
            console.print("✅ Gemini API 测试脚本存在")
        else:
            console.print("❌ [red]Gemini API 测试脚本不存在[/red]")
            return False

        # 检查网络诊断脚本
        network_diagnostic = Path("network_diagnostic.py")
        if network_diagnostic.exists():
            console.print("✅ 网络诊断脚本存在")
        else:
            console.print("❌ [red]网络诊断脚本不存在[/red]")
            return False

        # 检查离线模式脚本
        offline_mode = Path("offline_mode.py")
        if offline_mode.exists():
            console.print("✅ 离线模式脚本存在")
        else:
            console.print("❌ [red]离线模式脚本不存在[/red]")
            return False

        # 检查快速修复脚本
        quick_fix = Path("quick_fix.py")
        if quick_fix.exists():
            console.print("✅ 快速修复脚本存在")
        else:
            console.print("❌ [red]快速修复脚本不存在[/red]")
            return False

        console.print("✅ 所有 API 工具脚本完整")
        return True

    except Exception as e:
        console.print(f"❌ [red]API 工具测试失败: {e}[/red]")
        return False

def main():
    """主测试函数"""
    console.print(Panel(
        "[bold blue]PyJianYin 基础功能测试[/bold blue]\n" +
        "验证项目的基本功能是否正常工作",
        border_style="blue"
    ))
    
    tests = [
        ("模块导入", test_imports),
        ("配置功能", test_config),
        ("环境设置", test_environment),
        ("文件操作", test_file_operations),
        ("命令行界面", test_command_line),
        ("API 工具", test_api_tools)
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        console.print(f"\n{'='*50}")
        console.print(f"🧪 [bold]测试: {test_name}[/bold]")
        console.print('='*50)
        
        if test_func():
            console.print(f"✅ [green]{test_name} 测试通过[/green]")
            passed += 1
        else:
            console.print(f"❌ [red]{test_name} 测试失败[/red]")
    
    # 显示测试结果
    console.print(f"\n{'='*50}")
    console.print(f"📊 [bold]测试结果总结[/bold]")
    console.print('='*50)
    
    if passed == total:
        console.print(f"🎉 [bold green]所有测试通过！({passed}/{total})[/bold green]")
        console.print("\n✨ [green]项目基础功能正常，可以开始使用 PyJianYin！[/green]")
        
        console.print("\n📋 [bold]下一步建议:[/bold]")
        console.print("  1. 配置 .env 文件中的 Gemini API Key")
        console.print("  2. 在 my_video_assets 目录下添加素材")
        console.print("  3. 运行 'python main.py auto' 开始生成视频")
        
    else:
        console.print(f"⚠️ [yellow]部分测试失败 ({passed}/{total})[/yellow]")
        console.print("\n🔧 [yellow]请检查失败的测试项目并修复相关问题[/yellow]")
    
    return passed == total

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
