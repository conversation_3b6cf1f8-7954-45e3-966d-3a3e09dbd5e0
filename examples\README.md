# 示例文件说明

这个目录包含了 PyJianYin 项目的示例文件，用于测试和演示系统功能。

## 文件列表

### 字幕文件
- `example.srt` - 关于"人工智能对未来工作的影响"的示例字幕文件
- `funny_example.srt` - 搞笑内容的示例字幕文件
- `economics_example.srt` - 经济解读内容的示例字幕文件

### 音乐文件
由于版权原因，音乐文件需要用户自行准备。建议的音乐文件：

- `example.mp3` - 适合知识科普的背景音乐（节奏平稳，120 BPM左右）
- `funny_music.mp3` - 适合搞笑卡点的音乐（节奏明快，140+ BPM）
- `serious_music.mp3` - 适合经济解读的音乐（严肃专业，100-110 BPM）

### 配置文件
- `.env.example` - 环境变量配置模板

## 使用方法

1. **准备音乐文件**：
   - 将你的背景音乐文件放在这个目录下
   - 重命名为对应的文件名（如 `example.mp3`）

2. **配置环境变量**：
   ```bash
   cp .env.example .env
   # 编辑 .env 文件，填入你的 Gemini API Key
   ```

3. **准备素材**：
   - 在 `my_video_assets` 目录下放置你的视频和图片素材
   - 建议按类别组织文件夹结构

4. **运行示例**：
   ```bash
   python main.py
   ```

## 素材建议

为了获得最佳效果，建议准备以下类型的素材：

### 知识科普类
- 科技产品演示视频
- 数据图表和信息图
- 实验室场景
- 办公环境

### 经济解读类
- 股市图表
- 商务会议场景
- 工厂生产线
- 金融数据可视化

### 搞笑卡点类
- 有趣的动物视频
- 搞笑表情包
- 意外瞬间
- 网络梗图

## 注意事项

1. 确保所有素材文件都有合法的使用权限
2. 音乐文件建议使用无版权或已授权的音乐
3. 视频素材建议使用 MP4 格式，图片建议使用 JPG/PNG 格式
4. 文件名建议使用英文，避免特殊字符
