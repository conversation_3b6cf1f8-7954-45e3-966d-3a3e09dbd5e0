#!/usr/bin/env python3
"""
批量修复 Unicode 字符的脚本
"""

import re
from pathlib import Path

def fix_unicode_in_file(file_path: Path):
    """修复文件中的 Unicode 字符"""
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 定义替换规则
        replacements = {
            '❌': 'ERROR:',
            '✅': 'SUCCESS:',
            '⚠️': 'WARNING:',
            '💡': 'INFO:',
            '🔄': 'PROGRESS:',
            '🧪': 'TEST:',
            '📊': 'STATS:',
            '📁': 'FOLDER:',
            '🖼️': 'IMAGE:',
            '📝': 'TEXT:',
            '🎉': 'CELEBRATION:',
            '🔧': 'TOOL:',
            '📋': 'LIST:',
            '🎬': 'VIDEO:',
            '🎥': 'CAMERA:',
            '📏': 'RULER:',
            '🔑': 'KEY:',
            '🎯': 'TARGET:',
            '🚀': 'ROCKET:',
            '🔍': 'SEARCH:',
            '📹': 'VIDEOCAM:',
            '🎪': 'CIRCUS:',
            '🎭': 'THEATER:',
            '🎨': 'ART:',
            '🎵': 'MUSIC:',
            '🎶': 'NOTES:',
            '🎸': 'GUITAR:',
            '🎤': 'MIC:',
            '🎧': 'HEADPHONES:',
            '🎮': 'GAME:',
            '🎲': 'DICE:',
            '🎯': 'DART:',
            '🎪': 'TENT:',
            '🎭': 'MASKS:',
            '🎨': 'PALETTE:',
            '🎵': 'NOTE:',
            '🎶': 'NOTES:',
            '🎸': 'GUITAR:',
            '🎤': 'MICROPHONE:',
            '🎧': 'HEADPHONES:',
            '🎮': 'CONTROLLER:',
            '🎲': 'DICE:',
        }
        
        # 执行替换
        modified = False
        for emoji, replacement in replacements.items():
            if emoji in content:
                content = content.replace(emoji, replacement)
                modified = True
        
        # 如果有修改，写回文件
        if modified:
            with open(file_path, 'w', encoding='utf-8') as f:
                f.write(content)
            print(f"修复了 {file_path}")
            return True
        else:
            print(f"无需修复 {file_path}")
            return False
            
    except Exception as e:
        print(f"修复 {file_path} 时出错: {e}")
        return False

def main():
    """主函数"""
    print("开始修复 Unicode 字符...")
    
    # 需要修复的文件列表
    files_to_fix = [
        "test_new_api.py",
        "check_file_sizes.py",
        "video_splitter.py",
        "large_video_analyzer.py",
        "test_complete_workflow.py"
    ]
    
    fixed_count = 0
    
    for file_name in files_to_fix:
        file_path = Path(file_name)
        if file_path.exists():
            if fix_unicode_in_file(file_path):
                fixed_count += 1
        else:
            print(f"文件不存在: {file_name}")
    
    print(f"\n修复完成！共修复了 {fixed_count} 个文件。")

if __name__ == "__main__":
    main()
