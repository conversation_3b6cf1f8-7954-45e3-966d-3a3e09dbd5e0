{"canvas_config": {"width": 1920, "height": 1080, "ratio": "original"}, "color_space": 0, "config": {"adjust_max_index": 1, "attachment_info": [], "combination_max_index": 1, "export_range": null, "extract_audio_last_index": 1, "lyrics_recognition_id": "", "lyrics_sync": true, "lyrics_taskinfo": [], "maintrack_adsorb": true, "material_save_mode": 0, "multi_language_current": "none", "multi_language_list": [], "multi_language_main": "none", "multi_language_mode": "none", "original_sound_last_index": 1, "record_audio_last_index": 1, "sticker_max_index": 1, "subtitle_keywords_config": null, "subtitle_recognition_id": "", "subtitle_sync": true, "subtitle_taskinfo": [], "system_font_list": [], "video_mute": false, "zoom_info_params": null}, "cover": null, "create_time": 0, "duration": 30000000, "extra_info": null, "fps": 30, "free_render_index_mode_on": false, "group_container": null, "id": "91E08AC5-22FB-47e2-9AA0-7DC300FAEA2B", "keyframe_graph_list": [], "keyframes": {"adjusts": [], "audios": [], "effects": [], "filters": [], "handwrites": [], "stickers": [], "texts": [], "videos": []}, "last_modified_platform": {"app_id": 3704, "app_source": "lv", "app_version": "5.9.0", "os": "windows"}, "materials": {"ai_translates": [], "audio_balances": [], "audio_effects": [], "audio_fades": [], "audio_track_indexes": [], "audios": [{"app_id": 0, "category_id": "", "category_name": "local", "check_flag": 3, "copyright_limit_type": "none", "duration": 30041000, "effect_id": "", "formula_id": "", "id": "4536e7e32672357bafabb50f54b82d55", "local_material_id": "4536e7e32672357bafabb50f54b82d55", "music_id": "4536e7e32672357bafabb50f54b82d55", "name": "牵丝戏-1.mp3", "path": "E:\\pyjianyin\\my_video_assets\\music\\牵丝戏-1.mp3", "source_platform": 0, "type": "extract_music", "wave_points": []}], "beats": [], "canvases": [], "chromas": [], "color_curves": [], "digital_humans": [], "drafts": [], "effects": [], "flowers": [], "green_screens": [], "handwrites": [], "hsl": [], "images": [], "log_color_wheels": [], "loudnesses": [], "manual_deformations": [], "masks": [], "material_animations": [], "material_colors": [], "multi_language_refs": [], "placeholders": [], "plugin_effects": [], "primary_color_wheels": [], "realtime_denoises": [], "shapes": [], "smart_crops": [], "smart_relights": [], "sound_channel_mappings": [], "speeds": [{"curve_speed": null, "id": "b4889b6806044c67bdfa6eab40b9db81", "mode": 0, "speed": 1.0, "type": "speed"}, {"curve_speed": null, "id": "dec86fb97f3e46928617e1c1ab50921e", "mode": 0, "speed": 1.0, "type": "speed"}], "stickers": [], "tail_leaders": [], "text_templates": [], "texts": [{"id": "fa0696b9fb9649fbbf2c1c653197b4ae", "content": "{\"styles\": [{\"fill\": {\"alpha\": 1.0, \"content\": {\"render_type\": \"solid\", \"solid\": {\"alpha\": 1.0, \"color\": [1.0, 1.0, 1.0]}}}, \"range\": [0, 8], \"size\": 5.0, \"bold\": false, \"italic\": false, \"underline\": false, \"strokes\": []}], \"text\": \"欢迎观看本期视频\"}", "typesetting": 0, "alignment": 0, "letter_spacing": 0.0, "line_spacing": 0.02, "line_feed": 1, "line_max_width": 0.82, "force_apply_line_max_width": false, "check_flag": 7, "type": "subtitle", "global_alpha": 1.0}, {"id": "f0f9014a8f82438092db6faa4311a206", "content": "{\"styles\": [{\"fill\": {\"alpha\": 1.0, \"content\": {\"render_type\": \"solid\", \"solid\": {\"alpha\": 1.0, \"color\": [1.0, 1.0, 1.0]}}}, \"range\": [0, 16], \"size\": 5.0, \"bold\": false, \"italic\": false, \"underline\": false, \"strokes\": []}], \"text\": \"今天我们来聊聊人工智能的发展历程\"}", "typesetting": 0, "alignment": 0, "letter_spacing": 0.0, "line_spacing": 0.02, "line_feed": 1, "line_max_width": 0.82, "force_apply_line_max_width": false, "check_flag": 7, "type": "subtitle", "global_alpha": 1.0}, {"id": "f130d67e225b4fd3a8e017dfee4c3336", "content": "{\"styles\": [{\"fill\": {\"alpha\": 1.0, \"content\": {\"render_type\": \"solid\", \"solid\": {\"alpha\": 1.0, \"color\": [1.0, 1.0, 1.0]}}}, \"range\": [0, 12], \"size\": 5.0, \"bold\": false, \"italic\": false, \"underline\": false, \"strokes\": []}], \"text\": \"从早期的概念到现在的应用\"}", "typesetting": 0, "alignment": 0, "letter_spacing": 0.0, "line_spacing": 0.02, "line_feed": 1, "line_max_width": 0.82, "force_apply_line_max_width": false, "check_flag": 7, "type": "subtitle", "global_alpha": 1.0}, {"id": "704d95b771b64454b1a16ae31209bf05", "content": "{\"styles\": [{\"fill\": {\"alpha\": 1.0, \"content\": {\"render_type\": \"solid\", \"solid\": {\"alpha\": 1.0, \"color\": [1.0, 1.0, 1.0]}}}, \"range\": [0, 12], \"size\": 5.0, \"bold\": false, \"italic\": false, \"underline\": false, \"strokes\": []}], \"text\": \"AI技术经历了怎样的变化\"}", "typesetting": 0, "alignment": 0, "letter_spacing": 0.0, "line_spacing": 0.02, "line_feed": 1, "line_max_width": 0.82, "force_apply_line_max_width": false, "check_flag": 7, "type": "subtitle", "global_alpha": 1.0}, {"id": "52169dea5e0546999d8fccc8cd1699c2", "content": "{\"styles\": [{\"fill\": {\"alpha\": 1.0, \"content\": {\"render_type\": \"solid\", \"solid\": {\"alpha\": 1.0, \"color\": [1.0, 1.0, 1.0]}}}, \"range\": [0, 11], \"size\": 5.0, \"bold\": false, \"italic\": false, \"underline\": false, \"strokes\": []}], \"text\": \"让我们一起探索这个话题\"}", "typesetting": 0, "alignment": 0, "letter_spacing": 0.0, "line_spacing": 0.02, "line_feed": 1, "line_max_width": 0.82, "force_apply_line_max_width": false, "check_flag": 7, "type": "subtitle", "global_alpha": 1.0}, {"id": "eaadfbec2d28473bbc0493f8593d95d0", "content": "{\"styles\": [{\"fill\": {\"alpha\": 1.0, \"content\": {\"render_type\": \"solid\", \"solid\": {\"alpha\": 1.0, \"color\": [1.0, 1.0, 1.0]}}}, \"range\": [0, 7], \"size\": 5.0, \"bold\": false, \"italic\": false, \"underline\": false, \"strokes\": []}], \"text\": \"感谢大家的观看\"}", "typesetting": 0, "alignment": 0, "letter_spacing": 0.0, "line_spacing": 0.02, "line_feed": 1, "line_max_width": 0.82, "force_apply_line_max_width": false, "check_flag": 7, "type": "subtitle", "global_alpha": 1.0}], "time_marks": [], "transitions": [], "video_effects": [], "video_trackings": [], "videos": [{"audio_fade": null, "category_id": "", "category_name": "local", "check_flag": 63487, "crop": {"upper_left_x": 0.0, "upper_left_y": 0.0, "upper_right_x": 1.0, "upper_right_y": 0.0, "lower_left_x": 0.0, "lower_left_y": 1.0, "lower_right_x": 1.0, "lower_right_y": 1.0}, "crop_ratio": "free", "crop_scale": 1.0, "duration": 82867000, "height": 1920, "id": "bb1471c0725c391fa70cf359c1e141a2", "local_material_id": "", "material_id": "bb1471c0725c391fa70cf359c1e141a2", "material_name": "mao.mp4", "media_path": "", "path": "E:\\pyjianyin\\my_video_assets\\videos\\mao.mp4", "type": "video", "width": 1080}], "vocal_beautifys": [], "vocal_separations": []}, "mutable_config": null, "name": "", "new_version": "110.0.0", "relationships": [], "render_index_track_mode_on": false, "retouch_cover": null, "source": "default", "static_cover_image_path": "", "time_marks": null, "tracks": [{"attribute": 0, "flag": 0, "id": "6c6399e78f964118a15229564ce8e5fc", "is_default_name": false, "name": "video_main", "segments": [{"enable_adjust": true, "enable_color_correct_adjust": false, "enable_color_curves": true, "enable_color_match_adjust": false, "enable_color_wheels": true, "enable_lut": true, "enable_smart_color_adjust": false, "last_nonzero_volume": 1.0, "reverse": false, "track_attribute": 0, "track_render_index": 0, "visible": true, "id": "13d87f0680b040c0a7a7bb2326b03911", "material_id": "bb1471c0725c391fa70cf359c1e141a2", "target_timerange": {"start": 0, "duration": 5000000}, "common_keyframes": [], "keyframe_refs": [], "source_timerange": {"start": 0, "duration": 5000000}, "speed": 1.0, "volume": 1.0, "extra_material_refs": ["dec86fb97f3e46928617e1c1ab50921e"], "clip": {"alpha": 1.0, "flip": {"horizontal": false, "vertical": false}, "rotation": 0.0, "scale": {"x": 1.0, "y": 1.0}, "transform": {"x": 0.0, "y": 0.0}}, "uniform_scale": {"on": true, "value": 1.0}, "hdr_settings": {"intensity": 1.0, "mode": 1, "nits": 1000}, "render_index": 0}], "type": "video"}, {"attribute": 0, "flag": 0, "id": "e4d7b6b10e4b4668bc5a3be23d397fa8", "is_default_name": false, "name": "music", "segments": [{"enable_adjust": true, "enable_color_correct_adjust": false, "enable_color_curves": true, "enable_color_match_adjust": false, "enable_color_wheels": true, "enable_lut": true, "enable_smart_color_adjust": false, "last_nonzero_volume": 1.0, "reverse": false, "track_attribute": 0, "track_render_index": 0, "visible": true, "id": "214330cb6a1247f9986fa4a29716c52d", "material_id": "4536e7e32672357bafabb50f54b82d55", "target_timerange": {"start": 0, "duration": 30000000}, "common_keyframes": [], "keyframe_refs": [], "source_timerange": {"start": 0, "duration": 30000000}, "speed": 1.0, "volume": 1.0, "extra_material_refs": ["b4889b6806044c67bdfa6eab40b9db81"], "clip": null, "hdr_settings": null, "render_index": 0}], "type": "audio"}, {"attribute": 0, "flag": 0, "id": "ccec0deeafa6473ca158a458e2ad6f8b", "is_default_name": false, "name": "subtitles", "segments": [{"enable_adjust": true, "enable_color_correct_adjust": false, "enable_color_curves": true, "enable_color_match_adjust": false, "enable_color_wheels": true, "enable_lut": true, "enable_smart_color_adjust": false, "last_nonzero_volume": 1.0, "reverse": false, "track_attribute": 0, "track_render_index": 0, "visible": true, "id": "976cd812a5904306b3616ca19aa17c10", "material_id": "fa0696b9fb9649fbbf2c1c653197b4ae", "target_timerange": {"start": 0, "duration": 5000000}, "common_keyframes": [], "keyframe_refs": [], "source_timerange": null, "speed": 1.0, "volume": 1.0, "extra_material_refs": ["852dc30fa92a4bf08bc5f73866500050"], "clip": {"alpha": 1.0, "flip": {"horizontal": false, "vertical": false}, "rotation": 0.0, "scale": {"x": 1.0, "y": 1.0}, "transform": {"x": 0.0, "y": -0.8}}, "uniform_scale": {"on": true, "value": 1.0}, "render_index": 15010}, {"enable_adjust": true, "enable_color_correct_adjust": false, "enable_color_curves": true, "enable_color_match_adjust": false, "enable_color_wheels": true, "enable_lut": true, "enable_smart_color_adjust": false, "last_nonzero_volume": 1.0, "reverse": false, "track_attribute": 0, "track_render_index": 0, "visible": true, "id": "bea7dfc8ab63428687faccfe3bfe9260", "material_id": "f0f9014a8f82438092db6faa4311a206", "target_timerange": {"start": 5000000, "duration": 5000000}, "common_keyframes": [], "keyframe_refs": [], "source_timerange": null, "speed": 1.0, "volume": 1.0, "extra_material_refs": ["6d4a50d69abf4ff5ae98c1b778b88977"], "clip": {"alpha": 1.0, "flip": {"horizontal": false, "vertical": false}, "rotation": 0.0, "scale": {"x": 1.0, "y": 1.0}, "transform": {"x": 0.0, "y": -0.8}}, "uniform_scale": {"on": true, "value": 1.0}, "render_index": 15010}, {"enable_adjust": true, "enable_color_correct_adjust": false, "enable_color_curves": true, "enable_color_match_adjust": false, "enable_color_wheels": true, "enable_lut": true, "enable_smart_color_adjust": false, "last_nonzero_volume": 1.0, "reverse": false, "track_attribute": 0, "track_render_index": 0, "visible": true, "id": "6cd46d84bc6d4543a3da5bffc25d7d49", "material_id": "f130d67e225b4fd3a8e017dfee4c3336", "target_timerange": {"start": 10000000, "duration": 5000000}, "common_keyframes": [], "keyframe_refs": [], "source_timerange": null, "speed": 1.0, "volume": 1.0, "extra_material_refs": ["d7c9b42edc6f4be39e58f4a7321f3458"], "clip": {"alpha": 1.0, "flip": {"horizontal": false, "vertical": false}, "rotation": 0.0, "scale": {"x": 1.0, "y": 1.0}, "transform": {"x": 0.0, "y": -0.8}}, "uniform_scale": {"on": true, "value": 1.0}, "render_index": 15010}, {"enable_adjust": true, "enable_color_correct_adjust": false, "enable_color_curves": true, "enable_color_match_adjust": false, "enable_color_wheels": true, "enable_lut": true, "enable_smart_color_adjust": false, "last_nonzero_volume": 1.0, "reverse": false, "track_attribute": 0, "track_render_index": 0, "visible": true, "id": "f756ffbd79994f1290e2e1975161dc87", "material_id": "704d95b771b64454b1a16ae31209bf05", "target_timerange": {"start": 15000000, "duration": 5000000}, "common_keyframes": [], "keyframe_refs": [], "source_timerange": null, "speed": 1.0, "volume": 1.0, "extra_material_refs": ["2ff39bee00c743a9afefa44806f683bd"], "clip": {"alpha": 1.0, "flip": {"horizontal": false, "vertical": false}, "rotation": 0.0, "scale": {"x": 1.0, "y": 1.0}, "transform": {"x": 0.0, "y": -0.8}}, "uniform_scale": {"on": true, "value": 1.0}, "render_index": 15010}, {"enable_adjust": true, "enable_color_correct_adjust": false, "enable_color_curves": true, "enable_color_match_adjust": false, "enable_color_wheels": true, "enable_lut": true, "enable_smart_color_adjust": false, "last_nonzero_volume": 1.0, "reverse": false, "track_attribute": 0, "track_render_index": 0, "visible": true, "id": "c84bda54de6946f1afe6064e2ca9db3c", "material_id": "52169dea5e0546999d8fccc8cd1699c2", "target_timerange": {"start": 20000000, "duration": 5000000}, "common_keyframes": [], "keyframe_refs": [], "source_timerange": null, "speed": 1.0, "volume": 1.0, "extra_material_refs": ["81ef7e14289540f5932dbc16b700129c"], "clip": {"alpha": 1.0, "flip": {"horizontal": false, "vertical": false}, "rotation": 0.0, "scale": {"x": 1.0, "y": 1.0}, "transform": {"x": 0.0, "y": -0.8}}, "uniform_scale": {"on": true, "value": 1.0}, "render_index": 15010}, {"enable_adjust": true, "enable_color_correct_adjust": false, "enable_color_curves": true, "enable_color_match_adjust": false, "enable_color_wheels": true, "enable_lut": true, "enable_smart_color_adjust": false, "last_nonzero_volume": 1.0, "reverse": false, "track_attribute": 0, "track_render_index": 0, "visible": true, "id": "3f7cc44734174c0e828c1b40e4e9f96a", "material_id": "eaadfbec2d28473bbc0493f8593d95d0", "target_timerange": {"start": 25000000, "duration": 5000000}, "common_keyframes": [], "keyframe_refs": [], "source_timerange": null, "speed": 1.0, "volume": 1.0, "extra_material_refs": ["855db41df44948469a2e0e1c3b02c792"], "clip": {"alpha": 1.0, "flip": {"horizontal": false, "vertical": false}, "rotation": 0.0, "scale": {"x": 1.0, "y": 1.0}, "transform": {"x": 0.0, "y": -0.8}}, "uniform_scale": {"on": true, "value": 1.0}, "render_index": 15010}], "type": "text"}], "update_time": 0, "version": 360000}