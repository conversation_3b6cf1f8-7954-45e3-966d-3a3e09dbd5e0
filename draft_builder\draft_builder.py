import pyJianYingDraft as draft
import json
import os
import sys
from pathlib import Path
from typing import List, Dict, Optional, Tuple
import random
from rich.console import Console
from rich.progress import Progress

# 添加项目根目录到路径
sys.path.append(str(Path(__file__).parent.parent))
from config import get_config

# 初始化配置和控制台
config = get_config()
console = Console()

def calculate_asset_score(asset: Dict, required_tags: List[str], preferred_category: str = None, preferred_mood: str = None) -> float:
    """
    计算素材与需求的匹配分数

    Args:
        asset: 素材信息
        required_tags: 需要的标签
        preferred_category: 偏好的分类
        preferred_mood: 偏好的情绪

    Returns:
        float: 匹配分数 (0-1)
    """
    analysis = asset.get('analysis', {})
    asset_tags = set(analysis.get('tags', []))
    asset_category = analysis.get('category', '')
    asset_mood = analysis.get('mood', '')
    asset_quality = analysis.get('quality', 'medium')

    score = 0.0

    # 标签匹配 (权重: 0.5)
    if required_tags:
        tag_matches = len(set(required_tags) & asset_tags)
        tag_score = tag_matches / len(required_tags)
        score += tag_score * 0.5

    # 分类匹配 (权重: 0.2)
    if preferred_category and asset_category == preferred_category:
        score += 0.2

    # 情绪匹配 (权重: 0.15)
    if preferred_mood and asset_mood == preferred_mood:
        score += 0.15

    # 质量加分 (权重: 0.15)
    quality_scores = {'high': 0.15, 'medium': 0.1, 'low': 0.05}
    score += quality_scores.get(asset_quality, 0.05)

    return min(score, 1.0)

def find_best_asset(assets_db: List[Dict], required_tags: List[str], asset_type: str = None,
                   preferred_category: str = None, preferred_mood: str = None,
                   used_assets: set = None) -> Optional[str]:
    """
    智能查找最匹配的素材

    Args:
        assets_db: 素材数据库
        required_tags: 需要的标签
        asset_type: 素材类型 ('video' 或 'image')
        preferred_category: 偏好的分类
        preferred_mood: 偏好的情绪
        used_assets: 已使用的素材集合，避免重复

    Returns:
        str: 最佳匹配的素材路径
    """
    if used_assets is None:
        used_assets = set()

    candidates = []

    for asset in assets_db:
        # 过滤类型
        if asset_type and asset.get('type') != asset_type:
            continue

        # 避免重复使用
        asset_path = asset.get('path')
        if asset_path in used_assets:
            continue

        # 检查文件是否存在
        if not Path(asset_path).exists():
            continue

        # 计算匹配分数
        score = calculate_asset_score(asset, required_tags, preferred_category, preferred_mood)

        if score > 0:
            candidates.append((asset_path, score))

    if not candidates:
        console.print(f"⚠️ [yellow]未找到匹配的素材: tags={required_tags}, type={asset_type}[/yellow]")
        return None

    # 按分数排序，选择最佳匹配
    candidates.sort(key=lambda x: x[1], reverse=True)

    # 在前几个高分候选中随机选择，增加多样性
    top_candidates = [c for c in candidates if c[1] >= candidates[0][1] * 0.8]
    selected = random.choice(top_candidates[:3])

    return selected[0]

def get_transition_type(transition_name: str) -> Optional[draft.TransitionType]:
    """
    根据转场名称获取对应的枚举类型
    """
    try:
        # 安全的转场映射，只使用确认存在的转场类型
        transition_mapping = {
            "叠化": getattr(draft.TransitionType, "叠化", None),
            "闪白": getattr(draft.TransitionType, "闪白", None),
            "推拉": getattr(draft.TransitionType, "推拉", None),
            "擦除": getattr(draft.TransitionType, "擦除", None),
        }

        # 过滤掉不存在的转场类型
        valid_transitions = {k: v for k, v in transition_mapping.items() if v is not None}

        # 如果请求的转场不存在，使用默认的叠化
        if transition_name not in valid_transitions:
            console.print(f"⚠️ [yellow]转场类型 '{transition_name}' 不支持，使用默认叠化[/yellow]")
            return valid_transitions.get("叠化")

        return valid_transitions.get(transition_name)

    except Exception as e:
        console.print(f"⚠️ [yellow]获取转场类型失败: {e}，跳过转场[/yellow]")
        return None

def create_enhanced_text_style(mode: str) -> draft.TextStyle:
    """根据视频模式创建文本样式"""
    try:
        if mode == "知识科普":
            return draft.TextStyle(
                size=5.0,
                color=(1.0, 1.0, 1.0),
                auto_wrapping=True,
                stroke_width=2.0,
                stroke_color=(0.0, 0.0, 0.0)
            )
        elif mode == "经济解读":
            return draft.TextStyle(
                size=4.5,
                color=(1.0, 1.0, 0.9),
                auto_wrapping=True,
                stroke_width=1.5,
                stroke_color=(0.1, 0.1, 0.1)
            )
        elif mode == "搞笑卡点":
            return draft.TextStyle(
                size=6.0,
                color=(1.0, 1.0, 0.0),
                auto_wrapping=True,
                stroke_width=3.0,
                stroke_color=(0.0, 0.0, 0.0),
                bold=True
            )
        else:
            return draft.TextStyle(
                size=5.0,
                color=(1.0, 1.0, 1.0),
                auto_wrapping=True
            )
    except Exception as e:
        # 如果创建样式失败，返回基本样式
        console.print(f"⚠️ [yellow]创建文本样式失败，使用基本样式: {e}[/yellow]")
        return draft.TextStyle(
            size=5.0,
            color=(1.0, 1.0, 1.0),
            auto_wrapping=True
        )

def add_video_effects(segment: draft.VideoSegment, scene: Dict, mode: str):
    """为视频片段添加特效"""
    try:
        # 根据模式添加滤镜
        if mode == "经济解读":
            segment.add_filter(draft.FilterType.敦刻尔克)
        elif mode == "搞笑卡点":
            # 随机添加一些动感效果
            effects = [draft.FilterType.复古, draft.FilterType.暖阳]
            segment.add_filter(random.choice(effects))

        # 根据场景添加关键帧动画
        if scene.get("animation"):
            animation_type = scene["animation"]
            if animation_type == "zoom_in":
                segment.add_keyframe(0, draft.KeyframeProperty.uniform_scale, 1.0)
                segment.add_keyframe(scene.get("duration_seconds", 3) * 1000,
                                   draft.KeyframeProperty.uniform_scale, 1.2)
            elif animation_type == "pan_right":
                segment.add_keyframe(0, draft.KeyframeProperty.position_x, 0.0)
                segment.add_keyframe(scene.get("duration_seconds", 3) * 1000,
                                   draft.KeyframeProperty.position_x, 0.1)

    except Exception as e:
        console.print(f"⚠️ [yellow]添加特效失败: {e}[/yellow]")

def build_draft_from_storyboard(
    storyboard_json_path: str,
    assets_json_path: str,
    music_path: str = None,
    srt_path: str = None,
    output_draft_path: str = None,
    mode: str = "知识科普"
) -> bool:
    """
    根据剧本 JSON 构建剪映草稿

    Args:
        storyboard_json_path: 剧本文件路径
        assets_json_path: 素材数据库路径
        music_path: 音乐文件路径
        srt_path: 字幕文件路径
        output_draft_path: 输出草稿路径
        mode: 视频模式

    Returns:
        bool: 是否成功构建
    """
    console.print(f"🎬 [bold blue]构建剪映草稿[/bold blue]")

    # 检查文件存在性
    if not Path(storyboard_json_path).exists():
        console.print(f"❌ [red]剧本文件不存在: {storyboard_json_path}[/red]")
        return False

    if not Path(assets_json_path).exists():
        console.print(f"❌ [red]素材数据库不存在: {assets_json_path}[/red]")
        return False

    try:
        # 加载数据
        with open(storyboard_json_path, 'r', encoding='utf-8') as f:
            storyboard = json.load(f)

        with open(assets_json_path, 'r', encoding='utf-8') as f:
            assets_db = json.load(f)

        console.print(f"📋 加载了 {len(storyboard)} 个场景")
        console.print(f"📁 素材库包含 {len(assets_db)} 个文件")

        # 创建脚本
        script = draft.ScriptFile(1920, 1080)

        # 添加轨道
        video_track = "video_main"
        audio_track = "music"
        subtitle_track = "subtitles"

        script.add_track(draft.TrackType.video, video_track)
        script.add_track(draft.TrackType.audio, audio_track)
        script.add_track(draft.TrackType.text, subtitle_track, relative_index=10)

        # 添加背景音乐
        if music_path and Path(music_path).exists():
            try:
                # 计算总时长
                total_duration = sum(scene.get("duration_seconds", 3) for scene in storyboard)
                music_seg = draft.AudioSegment(
                    music_path,
                    draft.trange(0, f"{total_duration}s")
                )
                script.add_segment(music_seg, audio_track)
                console.print(f"🎵 添加背景音乐: {Path(music_path).name}")
            except Exception as e:
                console.print(f"⚠️ [yellow]添加音乐失败: {e}[/yellow]")

        # 导入字幕
        if srt_path and Path(srt_path).exists():
            try:
                subtitle_style = create_enhanced_text_style(mode)
                clip_settings = draft.ClipSettings(transform_y=-0.8)
                script.import_srt(
                    srt_path,
                    track_name=subtitle_track,
                    text_style=subtitle_style,
                    clip_settings=clip_settings
                )
                console.print(f"📝 导入字幕: {Path(srt_path).name}")
            except Exception as e:
                console.print(f"⚠️ [yellow]导入字幕失败: {e}[/yellow]")

        # 获取模式配置
        mode_config = config.get_video_mode_config(mode)
        preferred_tags = mode_config.get('preferred_tags', [])

        # 构建视频时间线
        current_time_us = 0
        last_video_segment = None
        used_assets = set()

        with Progress() as progress:
            task = progress.add_task("构建时间线...", total=len(storyboard))

            for i, scene in enumerate(storyboard):
                scene_type = scene.get("type", "video")
                duration_seconds = scene.get("duration_seconds", 3)
                duration_us = int(duration_seconds * 1_000_000)

                progress.update(task, description=f"处理场景 {i+1}/{len(storyboard)}")

                if scene_type in ["video", "image"]:
                    # 查找最佳素材
                    required_tags = scene.get("tags", [])
                    asset_path = find_best_asset(
                        assets_db=assets_db,
                        required_tags=required_tags,
                        asset_type=scene_type,
                        preferred_category=mode_config.get('preferred_tags', [None])[0] if mode_config.get('preferred_tags') else None,
                        used_assets=used_assets
                    )

                    if not asset_path:
                        console.print(f"⚠️ [yellow]场景 {i+1}: 未找到匹配素材 {required_tags}[/yellow]")
                        progress.advance(task)
                        continue

                    # 创建视频片段
                    try:
                        video_seg = draft.VideoSegment(
                            asset_path,
                            draft.Timerange(current_time_us, duration_us)
                        )

                        # 添加特效
                        try:
                            add_video_effects(video_seg, scene, mode)
                        except Exception as e:
                            console.print(f"⚠️ [yellow]添加特效失败: {e}[/yellow]")

                        # 添加转场
                        if last_video_segment and scene.get("transition"):
                            transition_info = scene["transition"]
                            transition_name = transition_info.get("name", "叠化")
                            transition_type = get_transition_type(transition_name)

                            if transition_type:
                                try:
                                    last_video_segment.add_transition(transition_type)
                                except Exception as e:
                                    console.print(f"⚠️ [yellow]添加转场 '{transition_name}' 失败: {e}[/yellow]")

                        script.add_segment(video_seg, video_track)
                        used_assets.add(asset_path)
                        current_time_us += duration_us
                        last_video_segment = video_seg

                    except Exception as e:
                        console.print(f"❌ [red]创建视频片段失败 (文件: {Path(asset_path).name}): {e}[/red]")
                        # 即使创建失败，也要推进时间，避免卡住
                        current_time_us += duration_us

                progress.advance(task)

        # 保存草稿
        if output_draft_path is None:
            output_draft_path = config.project_root / "draft_content.json"

        output_path = Path(output_draft_path)
        output_path.parent.mkdir(parents=True, exist_ok=True)

        script.dump(str(output_path))

        console.print(f"✅ [green]草稿构建成功: {output_path}[/green]")
        console.print(f"📊 总时长: {current_time_us / 1_000_000:.1f} 秒")
        console.print(f"🎬 使用素材: {len(used_assets)} 个")

        return True

    except Exception as e:
        console.print(f"❌ [red]草稿构建失败: {e}[/red]")
        return False

if __name__ == "__main__":
    console.print("🎬 [bold blue]PyJianYin 草稿构建器[/bold blue]")

    # 示例用法
    success = build_draft_from_storyboard(
        storyboard_json_path="../storyboard_generator/storyboard.json",
        assets_json_path="../asset_manager/assets.json",
        music_path="../examples/example.mp3",
        srt_path="../examples/example.srt",
        output_draft_path="draft_content.json",
        mode="知识科普"
    )

    if success:
        console.print("🎉 [green]草稿构建完成！[/green]")
    else:
        console.print("💥 [red]草稿构建失败[/red]")
        sys.exit(1)