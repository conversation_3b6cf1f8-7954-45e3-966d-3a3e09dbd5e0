#!/usr/bin/env python3
"""
基于 del.py 示例的新 API 测试脚本
使用最新的 Google GenAI SDK 调用方式
"""

import os
import sys
import time
import tempfile
from pathlib import Path
from rich.console import Console
from rich.panel import Panel
from rich.progress import Progress, SpinnerColumn, TextColumn

# 添加项目根目录到路径
sys.path.append(str(Path(__file__).parent))
from config import get_config

console = Console()
config = get_config()

def test_new_api_text():
    """测试新 API 的文本处理"""
    console.print("[bold]测试新 API 文本处理...[/bold]")
    
    try:
        from google import genai
        from google.genai import types
        
        # 设置 API Key
        os.environ['GOOGLE_API_KEY'] = config.gemini_api_key
        client = genai.Client()
        
        # 测试文本生成
        test_prompts = [
            "请用中文简单介绍什么是人工智能",
            "Generate a JSON object with name and description for a video editing tool",
            "解释一下视频剪辑中的转场效果"
        ]
        
        results = []
        
        with Progress(
            SpinnerColumn(),
            TextColumn("[progress.description]{task.description}"),
            console=console
        ) as progress:
            
            for i, prompt in enumerate(test_prompts):
                task = progress.add_task(f"测试提示 {i+1}/{len(test_prompts)}...", total=None)
                
                try:
                    response = client.models.generate_content(
                        model='models/gemini-2.5-flash',
                        contents=prompt
                    )
                    
                    if response and response.text:
                        results.append({
                            "prompt": prompt[:30] + "..." if len(prompt) > 30 else prompt,
                            "status": "SUCCESS",
                            "response": response.text[:100] + "..." if len(response.text) > 100 else response.text
                        })
                    else:
                        results.append({
                            "prompt": prompt[:30] + "..." if len(prompt) > 30 else prompt,
                            "status": "NO_RESPONSE",
                            "response": ""
                        })
                        
                except Exception as e:
                    results.append({
                        "prompt": prompt[:30] + "..." if len(prompt) > 30 else prompt,
                        "status": f"ERROR: {str(e)[:20]}",
                        "response": ""
                    })
                
                progress.remove_task(task)
        
        # 显示结果
        console.print("\nLIST: 文本处理结果:")
        for i, result in enumerate(results, 1):
            console.print(f"\n{i}. {result['prompt']}")
            console.print(f"   状态: {result['status']}")
            if result['response']:
                console.print(f"   响应: {result['response']}")
        
        success_count = sum(1 for r in results if "SUCCESS" in r["status"])
        console.print(f"\nSTATS: 文本处理测试: {success_count}/{len(test_prompts)} 成功")
        
        return success_count > 0
        
    except ImportError:
        console.print("ERROR: [red]新版 Google GenAI SDK 未安装[/red]")
        console.print("[yellow]请运行: pip install google-genai[/yellow]")
        return False
    except Exception as e:
        console.print(f"ERROR: [red]文本处理测试失败: {e}[/red]")
        return False

def test_new_api_image():
    """测试新 API 的图片处理"""
    console.print("\n[bold]测试新 API 图片处理...[/bold]")
    
    try:
        from google import genai
        from google.genai import types
        
        # 设置 API Key
        os.environ['GOOGLE_API_KEY'] = config.gemini_api_key
        client = genai.Client()
        
        # 创建测试图片或使用现有图片
        test_image_path = create_test_image()
        
        if not test_image_path:
            console.print("WARNING: [yellow]跳过图片测试（无法创建或找到测试图片）[/yellow]")
            return True
        
        try:
            with Progress(
                SpinnerColumn(),
                TextColumn("[progress.description]{task.description}"),
                console=console
            ) as progress:
                
                # 读取图片数据
                task = progress.add_task("读取图片数据...", total=None)
                with open(test_image_path, 'rb') as f:
                    image_data = f.read()
                
                # 根据示例代码的方式处理图片
                image_part = types.Part(
                    inline_data=types.Blob(
                        data=image_data,
                        mime_type='image/png' if test_image_path.endswith('.png') else 'image/jpeg'
                    )
                )
                
                progress.remove_task(task)
                
                # 测试图片分析
                test_prompts = [
                    "描述这张图片的内容",
                    "这张图片中有什么颜色？",
                    "请用JSON格式分析这张图片，包含description和colors字段"
                ]
                
                results = []
                
                for i, prompt in enumerate(test_prompts):
                    task = progress.add_task(f"图片分析 {i+1}/{len(test_prompts)}...", total=None)
                    
                    try:
                        # 使用新 API 的多模态内容格式
                        content = types.Content(parts=[
                            types.Part(text=prompt),
                            image_part
                        ])
                        
                        response = client.models.generate_content(
                            model='models/gemini-2.5-flash',
                            contents=content
                        )
                        
                        if response and response.text:
                            results.append({
                                "prompt": prompt,
                                "status": "SUCCESS: 成功",
                                "response": response.text[:150] + "..." if len(response.text) > 150 else response.text
                            })
                        else:
                            results.append({
                                "prompt": prompt,
                                "status": "ERROR: 无响应",
                                "response": ""
                            })
                            
                    except Exception as e:
                        results.append({
                            "prompt": prompt,
                            "status": f"ERROR: 错误: {str(e)[:30]}",
                            "response": ""
                        })
                    
                    progress.remove_task(task)
            
            # 显示结果
            console.print("\nLIST: 图片分析结果:")
            for i, result in enumerate(results, 1):
                console.print(f"\n{i}. {result['prompt']}")
                console.print(f"   状态: {result['status']}")
                if result['response']:
                    console.print(f"   响应: {result['response']}")
            
            success_count = sum(1 for r in results if "SUCCESS:" in r["status"])
            console.print(f"\nSTATS: 图片处理测试: {success_count}/{len(test_prompts)} 成功")
            
            return success_count > 0
            
        finally:
            # 清理测试文件
            if test_image_path and Path(test_image_path).exists():
                try:
                    os.unlink(test_image_path)
                except:
                    pass
        
    except ImportError:
        console.print("ERROR: [red]新版 Google GenAI SDK 未安装[/red]")
        return False
    except Exception as e:
        console.print(f"ERROR: [red]图片处理测试失败: {e}[/red]")
        return False

def create_test_image():
    """创建测试图片（确保小于 20MB）"""
    try:
        from PIL import Image, ImageDraw

        # 创建一个适中大小的测试图片（确保小于 20MB）
        img = Image.new('RGB', (800, 600), color='lightblue')
        draw = ImageDraw.Draw(img)

        # 绘制一些内容
        draw.rectangle([50, 50, 750, 550], fill='white', outline='black', width=3)
        draw.text((200, 200), "测试图片 - 20MB 限制", fill='black')
        draw.text((200, 250), "Test Image - 20MB Limit", fill='black')
        draw.ellipse([300, 350, 500, 500], fill='red', outline='darkred', width=3)

        # 保存到临时文件
        temp_file = tempfile.NamedTemporaryFile(suffix='.png', delete=False)
        img.save(temp_file.name, 'PNG', quality=85)  # 控制质量以确保文件大小

        # 检查文件大小
        file_size = Path(temp_file.name).stat().st_size
        size_mb = file_size / 1024 / 1024
        console.print(f"RULER: 创建测试图片: {size_mb:.1f}MB")

        return temp_file.name
        
    except ImportError:
        # 如果 PIL 不可用，尝试使用现有的图片文件
        for img_path in config.assets_dir.rglob("*.png"):
            if img_path.exists():
                console.print(f"FOLDER: 使用现有图片: {img_path.name}")
                return str(img_path)
        
        for img_path in config.assets_dir.rglob("*.jpg"):
            if img_path.exists():
                console.print(f"FOLDER: 使用现有图片: {img_path.name}")
                return str(img_path)
        
        console.print("WARNING: [yellow]无法创建或找到测试图片[/yellow]")
        return None

def main():
    """主测试函数"""
    console.print(Panel(
        "[bold blue]新 Google GenAI SDK 测试[/bold blue]\n" +
        "基于 del.py 示例的 API 调用方式",
        border_style="blue"
    ))
    
    # 检查 API Key
    if not config.validate_api_key():
        console.print("ERROR: [red]API Key 未配置，请先配置 GEMINI_API_KEY[/red]")
        return False
    
    # 安全显示 API Key
    if config.gemini_api_key and len(config.gemini_api_key) > 10:
        api_key_display = f"{config.gemini_api_key[:10]}...{config.gemini_api_key[-4:]}"
    else:
        api_key_display = "已配置"
    console.print(f"[green]API Key:[/green] {api_key_display}")
    
    # 运行测试
    tests = [
        ("文本处理", test_new_api_text),
        ("图片处理", test_new_api_image)
    ]
    
    results = {}
    
    for test_name, test_func in tests:
        console.print(f"\n{'='*50}")
        console.print(f"[bold]开始测试: {test_name}[/bold]")
        console.print('='*50)
        
        try:
            results[test_name] = test_func()
        except Exception as e:
            console.print(f"[red]ERROR:[/red] {test_name} 测试异常: {e}")
            results[test_name] = False
    
    # 显示总结
    console.print(f"\n{'='*50}")
    console.print("[bold]测试结果总结[/bold]")
    console.print('='*50)

    passed_tests = sum(results.values())
    total_tests = len(results)

    for test_name, success in results.items():
        status = "PASS" if success else "FAIL"
        style = "green" if success else "red"
        console.print(f"  [{style}]{status}[/{style}]: {test_name}")

    if passed_tests == total_tests:
        console.print(f"\n[bold green]所有测试通过！({passed_tests}/{total_tests})[/bold green]")
        console.print("[green]新版 Google GenAI SDK 工作正常！[/green]")
    else:
        console.print(f"\n[yellow]部分测试失败 ({passed_tests}/{total_tests})[/yellow]")
        console.print("[yellow]请检查 SDK 安装和 API 配置[/yellow]")
    
    return passed_tests == total_tests

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
