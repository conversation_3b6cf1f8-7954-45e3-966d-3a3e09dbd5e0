#!/usr/bin/env python3
"""
安装新版 Google GenAI SDK 的辅助脚本
"""

import subprocess
import sys
from pathlib import Path
from rich.console import Console
from rich.panel import Panel
from rich.progress import Progress, SpinnerColumn, TextColumn

console = Console()

def check_current_sdk():
    """检查当前安装的 SDK"""
    console.print("🔍 [bold]检查当前 SDK 状态...[/bold]")
    
    # 检查旧版 SDK
    try:
        import google.generativeai
        old_version = getattr(google.generativeai, '__version__', 'unknown')
        console.print(f"📦 旧版 SDK (google-generativeai): {old_version}")
    except ImportError:
        console.print("📦 旧版 SDK (google-generativeai): 未安装")
    
    # 检查新版 SDK
    try:
        from google import genai
        console.print("📦 新版 SDK (google-genai): 已安装")
        return True
    except ImportError:
        console.print("📦 新版 SDK (google-genai): 未安装")
        return False

def install_new_sdk():
    """安装新版 SDK"""
    console.print("\n📥 [bold blue]安装新版 Google GenAI SDK...[/bold blue]")
    
    try:
        with Progress(
            SpinnerColumn(),
            TextColumn("[progress.description]{task.description}"),
            console=console
        ) as progress:
            
            task = progress.add_task("正在安装 google-genai...", total=None)
            
            # 安装新版 SDK
            result = subprocess.run([
                sys.executable, "-m", "pip", "install", "google-genai>=1.0.0"
            ], capture_output=True, text=True)
            
            progress.remove_task(task)
            
            if result.returncode == 0:
                console.print("✅ [green]新版 SDK 安装成功！[/green]")
                return True
            else:
                console.print("❌ [red]新版 SDK 安装失败[/red]")
                console.print(f"错误信息: {result.stderr}")
                return False
                
    except Exception as e:
        console.print(f"❌ [red]安装过程出错: {e}[/red]")
        return False

def test_new_sdk():
    """测试新版 SDK"""
    console.print("\n🧪 [bold]测试新版 SDK...[/bold]")
    
    try:
        from google import genai
        from google.genai import types
        
        console.print("✅ 导入成功")
        
        # 检查 API Key
        import os
        sys.path.append(str(Path(__file__).parent))
        from config import get_config
        
        config = get_config()
        if config.validate_api_key():
            console.print("✅ API Key 已配置")
            
            # 简单测试
            os.environ['GOOGLE_API_KEY'] = config.gemini_api_key
            client = genai.Client()
            
            response = client.models.generate_content(
                model='models/gemini-2.5-flash',
                contents="Hello, this is a test."
            )
            
            if response and response.text:
                console.print("✅ API 调用成功")
                console.print(f"📝 响应: {response.text[:50]}...")
                return True
            else:
                console.print("❌ API 调用失败")
                return False
        else:
            console.print("⚠️ [yellow]API Key 未配置，跳过 API 测试[/yellow]")
            return True
            
    except ImportError as e:
        console.print(f"❌ [red]导入失败: {e}[/red]")
        return False
    except Exception as e:
        console.print(f"❌ [red]测试失败: {e}[/red]")
        return False

def update_requirements():
    """更新 requirements.txt"""
    console.print("\n📝 [bold]更新依赖文件...[/bold]")
    
    try:
        requirements_file = Path("requirements.txt")
        if requirements_file.exists():
            with open(requirements_file, 'r', encoding='utf-8') as f:
                content = f.read()
            
            # 检查是否已经包含新版 SDK
            if 'google-genai' not in content:
                # 在文件开头添加新版 SDK
                new_content = "# 新版 Google GenAI SDK (推荐)\ngoogle-genai>=1.0.0\n\n" + content
                
                with open(requirements_file, 'w', encoding='utf-8') as f:
                    f.write(new_content)
                
                console.print("✅ requirements.txt 已更新")
            else:
                console.print("✅ requirements.txt 已包含新版 SDK")
        else:
            console.print("⚠️ [yellow]requirements.txt 不存在[/yellow]")
            
    except Exception as e:
        console.print(f"❌ [red]更新 requirements.txt 失败: {e}[/red]")

def show_migration_tips():
    """显示迁移提示"""
    console.print("\n💡 [bold blue]迁移提示[/bold blue]")
    
    tips = [
        "新版 SDK 使用 Gemini 2.5 模型，性能更好",
        "API 调用方式略有不同，但项目已自动适配",
        "建议运行 'python main.py test-new-api' 测试功能",
        "如有问题，可查看 docs/Gemini_API_Migration.md",
        "旧版 SDK 仍可使用，但建议迁移到新版"
    ]
    
    for i, tip in enumerate(tips, 1):
        console.print(f"  {i}. {tip}")

def main():
    """主函数"""
    console.print(Panel(
        "[bold blue]Google GenAI SDK 安装助手[/bold blue]\n" +
        "帮助你安装和配置最新的 Gemini API SDK",
        border_style="blue"
    ))
    
    # 检查当前状态
    has_new_sdk = check_current_sdk()
    
    if has_new_sdk:
        console.print("\n🎉 [green]新版 SDK 已安装！[/green]")
        
        # 测试功能
        if test_new_sdk():
            console.print("\n✅ [green]新版 SDK 工作正常[/green]")
        else:
            console.print("\n⚠️ [yellow]新版 SDK 可能存在问题[/yellow]")
    else:
        console.print("\n📥 [yellow]需要安装新版 SDK[/yellow]")
        
        # 询问是否安装
        from rich.prompt import Confirm
        if Confirm.ask("是否现在安装新版 Google GenAI SDK？"):
            if install_new_sdk():
                console.print("\n🎉 [green]安装完成！[/green]")
                
                # 测试新安装的 SDK
                if test_new_sdk():
                    console.print("\n✅ [green]新版 SDK 工作正常[/green]")
                    update_requirements()
                else:
                    console.print("\n⚠️ [yellow]新版 SDK 可能存在问题[/yellow]")
            else:
                console.print("\n❌ [red]安装失败[/red]")
                return False
        else:
            console.print("\n⏭️ 跳过安装")
            return False
    
    # 显示迁移提示
    show_migration_tips()
    
    # 建议下一步
    console.print("\n📋 [bold]建议的下一步:[/bold]")
    console.print("  1. 运行 'python main.py test-new-api' 测试新版 API")
    console.print("  2. 运行 'python main.py analyze' 使用新版进行素材分析")
    console.print("  3. 查看 docs/Gemini_API_Migration.md 了解更多详情")
    
    return True

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
