#!/usr/bin/env python3
"""
离线模式工具
在网络不稳定时提供基本的素材管理和草稿构建功能
"""

import json
import sys
from pathlib import Path
from typing import List, Dict
from rich.console import Console
from rich.table import Table
from rich.panel import Panel

# 添加项目根目录到路径
sys.path.append(str(Path(__file__).parent))
from config import get_config

console = Console()
config = get_config()

def create_basic_asset_metadata(file_path: str) -> Dict:
    """
    创建基础的素材元数据（不依赖 AI 分析）
    
    Args:
        file_path: 文件路径
        
    Returns:
        dict: 基础元数据
    """
    path = Path(file_path)
    
    # 根据文件路径推断类别
    category = "unknown"
    tags = []
    mood = "neutral"
    
    # 根据文件夹名称推断类别
    parent_name = path.parent.name.lower()
    if "knowledge" in parent_name or "education" in parent_name:
        category = "knowledge"
        tags = ["education", "learning", "information"]
        mood = "serious"
    elif "funny" in parent_name or "humor" in parent_name:
        category = "funny_moment"
        tags = ["funny", "entertainment", "humor"]
        mood = "humorous"
    elif "economics" in parent_name or "business" in parent_name:
        category = "economics"
        tags = ["business", "economics", "professional"]
        mood = "serious"
    elif "technology" in parent_name or "tech" in parent_name:
        category = "knowledge"
        tags = ["technology", "innovation", "modern"]
        mood = "techy"
    elif "nature" in parent_name:
        category = "knowledge"
        tags = ["nature", "environment", "peaceful"]
        mood = "calm"
    
    # 根据文件名推断更多标签
    filename_lower = path.stem.lower()
    if any(word in filename_lower for word in ["chart", "graph", "data"]):
        tags.extend(["chart", "data", "visualization"])
    if any(word in filename_lower for word in ["background", "bg"]):
        tags.extend(["background", "simple"])
    if any(word in filename_lower for word in ["logo", "icon"]):
        tags.extend(["logo", "icon", "symbol"])
    
    # 去重
    tags = list(set(tags))
    
    return {
        "description": f"基于文件路径自动生成的描述: {path.name}",
        "tags": tags,
        "mood": mood,
        "category": category,
        "quality": "medium",  # 默认质量
        "duration_category": "medium"  # 默认时长类别
    }

def scan_assets_offline(root_dir: str = None) -> List[Dict]:
    """
    离线扫描素材文件
    
    Args:
        root_dir: 素材根目录
        
    Returns:
        list: 素材列表
    """
    if root_dir is None:
        root_dir = config.assets_dir
    
    root_path = Path(root_dir)
    if not root_path.exists():
        console.print(f"❌ [red]素材目录不存在: {root_path}[/red]")
        return []
    
    assets = []
    
    console.print("📁 [bold]扫描素材文件...[/bold]")
    
    for file_path in root_path.rglob("*"):
        if file_path.is_file() and config.is_supported_file(str(file_path)):
            # 确定文件类型
            file_type = "image"
            if file_path.suffix.lower() in config.supported_video_formats:
                file_type = "video"
            
            # 获取文件信息
            try:
                file_stat = file_path.stat()
                file_size = file_stat.st_size
                
                # 创建基础元数据
                analysis = create_basic_asset_metadata(str(file_path))
                
                asset_data = {
                    "path": str(file_path),
                    "type": file_type,
                    "analysis": analysis,
                    "file_size": file_size,
                    "analyzed_at": str(file_stat.st_mtime),
                    "offline_mode": True  # 标记为离线模式生成
                }
                
                assets.append(asset_data)
                console.print(f"✅ 扫描: {file_path.name}")
                
            except Exception as e:
                console.print(f"⚠️ [yellow]跳过文件: {file_path.name} - {e}[/yellow]")
    
    return assets

def create_offline_assets_db(output_json: str = None) -> bool:
    """
    创建离线素材数据库
    
    Args:
        output_json: 输出文件路径
        
    Returns:
        bool: 是否成功
    """
    if output_json is None:
        output_json = config.assets_json
    
    console.print("🔄 [bold blue]创建离线素材数据库[/bold blue]")
    
    # 扫描素材
    assets = scan_assets_offline()
    
    if not assets:
        console.print("⚠️ [yellow]未找到任何素材文件[/yellow]")
        return False
    
    # 保存到文件
    try:
        output_path = Path(output_json)
        output_path.parent.mkdir(parents=True, exist_ok=True)
        
        with open(output_path, "w", encoding="utf-8") as f:
            json.dump(assets, f, ensure_ascii=False, indent=2)
        
        console.print(f"✅ [green]离线素材数据库创建成功: {output_path}[/green]")
        console.print(f"📊 总计扫描: {len(assets)} 个文件")
        
        # 显示统计信息
        show_offline_summary(assets)
        
        return True
        
    except Exception as e:
        console.print(f"❌ [red]保存失败: {e}[/red]")
        return False

def show_offline_summary(assets: List[Dict]):
    """显示离线扫描结果摘要"""
    if not assets:
        return
    
    # 统计信息
    stats = {
        "total": len(assets),
        "videos": sum(1 for a in assets if a["type"] == "video"),
        "images": sum(1 for a in assets if a["type"] == "image"),
        "categories": {},
        "moods": {}
    }
    
    for asset in assets:
        analysis = asset.get("analysis", {})
        category = analysis.get("category", "unknown")
        mood = analysis.get("mood", "unknown")
        
        stats["categories"][category] = stats["categories"].get(category, 0) + 1
        stats["moods"][mood] = stats["moods"].get(mood, 0) + 1
    
    # 创建统计表格
    table = Table(title="📊 离线扫描摘要")
    table.add_column("类型", style="cyan")
    table.add_column("数量", style="magenta")
    
    table.add_row("总文件数", str(stats["total"]))
    table.add_row("视频文件", str(stats["videos"]))
    table.add_row("图片文件", str(stats["images"]))
    
    console.print(table)
    
    # 显示分类统计
    if stats["categories"]:
        console.print("\n📂 [bold]内容分类:[/bold]")
        for category, count in sorted(stats["categories"].items(), key=lambda x: x[1], reverse=True):
            console.print(f"  • {category}: {count}")
    
    if stats["moods"]:
        console.print("\n🎭 [bold]情绪分布:[/bold]")
        for mood, count in sorted(stats["moods"].items(), key=lambda x: x[1], reverse=True):
            console.print(f"  • {mood}: {count}")

def create_simple_storyboard(theme: str, mode: str, assets_json: str, output_json: str) -> bool:
    """
    创建简单的剧本（基于规则，不使用 AI）
    
    Args:
        theme: 视频主题
        mode: 视频模式
        assets_json: 素材数据库路径
        output_json: 输出剧本路径
        
    Returns:
        bool: 是否成功
    """
    console.print("📝 [bold blue]创建简单剧本（离线模式）[/bold blue]")
    
    # 加载素材数据库
    try:
        with open(assets_json, 'r', encoding='utf-8') as f:
            assets = json.load(f)
    except Exception as e:
        console.print(f"❌ [red]无法加载素材数据库: {e}[/red]")
        return False
    
    if not assets:
        console.print("❌ [red]素材数据库为空[/red]")
        return False
    
    # 根据模式选择素材
    mode_config = config.get_video_mode_config(mode)
    preferred_tags = mode_config.get('preferred_tags', [])
    
    # 筛选相关素材
    relevant_assets = []
    for asset in assets:
        asset_tags = set(asset.get('analysis', {}).get('tags', []))
        if any(tag in asset_tags for tag in preferred_tags):
            relevant_assets.append(asset)
    
    if not relevant_assets:
        # 如果没有匹配的，使用所有素材
        relevant_assets = assets
        console.print("⚠️ [yellow]未找到完全匹配的素材，使用所有可用素材[/yellow]")
    
    # 创建简单的剧本
    storyboard = []
    total_duration = 0
    target_duration = 60  # 目标60秒
    
    while total_duration < target_duration and relevant_assets:
        # 随机选择一个素材
        import random
        asset = random.choice(relevant_assets)
        
        # 确定场景时长
        if mode == "搞笑卡点":
            duration = random.uniform(0.5, 2.0)  # 快节奏
        elif mode == "知识科普":
            duration = random.uniform(3.0, 6.0)  # 中等节奏
        else:
            duration = random.uniform(2.0, 5.0)  # 默认节奏
        
        scene = {
            "type": asset["type"],
            "tags": asset.get('analysis', {}).get('tags', []),
            "duration_seconds": duration,
            "timing": total_duration,
            "comment": f"基于 {mode} 模式自动选择的 {asset['type']} 素材"
        }
        
        # 添加转场
        if storyboard:  # 不是第一个场景
            transitions = mode_config.get('transitions', ['叠化'])
            scene["transition"] = {
                "name": random.choice(transitions),
                "duration": 0.5
            }
        
        storyboard.append(scene)
        total_duration += duration
    
    # 保存剧本
    try:
        output_path = Path(output_json)
        output_path.parent.mkdir(parents=True, exist_ok=True)
        
        with open(output_path, "w", encoding="utf-8") as f:
            json.dump(storyboard, f, ensure_ascii=False, indent=2)
        
        console.print(f"✅ [green]简单剧本创建成功: {output_path}[/green]")
        console.print(f"📊 生成了 {len(storyboard)} 个场景，总时长 {total_duration:.1f} 秒")
        
        return True
        
    except Exception as e:
        console.print(f"❌ [red]保存剧本失败: {e}[/red]")
        return False

def main():
    """主函数"""
    console.print(Panel(
        "[bold blue]PyJianYin 离线模式[/bold blue]\n" +
        "在网络不稳定时提供基本功能",
        border_style="blue"
    ))
    
    console.print("\n🔧 [bold]可用功能:[/bold]")
    console.print("  1. 扫描素材文件（基于文件路径推断属性）")
    console.print("  2. 创建简单剧本（基于规则，不使用 AI）")
    console.print("  3. 构建剪映草稿（使用现有功能）")
    
    console.print("\n⚠️ [yellow]注意事项:[/yellow]")
    console.print("  • 离线模式的素材分析准确性较低")
    console.print("  • 剧本生成基于简单规则，创意性有限")
    console.print("  • 建议网络恢复后重新进行 AI 分析")
    
    # 创建离线素材数据库
    if create_offline_assets_db():
        console.print("\n✅ [green]离线模式设置完成！[/green]")
        console.print("\n📋 [bold]下一步:[/bold]")
        console.print("  1. 使用 'python main.py build' 构建草稿")
        console.print("  2. 网络恢复后运行 'python main.py analyze' 进行 AI 分析")
    else:
        console.print("\n❌ [red]离线模式设置失败[/red]")

if __name__ == "__main__":
    main()
