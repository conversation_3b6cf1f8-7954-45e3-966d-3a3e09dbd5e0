# Gemini API 迁移指南

## 🔄 重要更新 (2025年1月)

Google 推出了新的 **Google GenAI SDK** 来替代旧的 `google-generativeai` 库。PyJianYin 已更新以支持最新的 API。

## 📋 主要变化

### 新 SDK vs 旧 SDK

| 特性 | 旧 SDK | 新 SDK |
|------|--------|--------|
| 包名 | `google-generativeai` | `google-genai` |
| 支持状态 | 2025年9月停止支持 | 推荐使用 |
| 模型名称 | `gemini-1.5-pro-latest` | `gemini-2.5-flash` |
| API 风格 | 分散的函数调用 | 统一的 Client 对象 |
| 文件大小限制 | 50MB | **20MB** |

### 最新模型

- **Gemini 2.5 Flash**: 最新的高性能模型
- **Gemini 2.5 Pro**: 更强大的专业模型

### ⚠️ 重要变化：文件大小限制

新版 API 将文件大小限制从 50MB 降低到 **20MB**：

- **影响**: 较大的视频和图片文件需要压缩
- **检查**: 使用 `python main.py check-sizes` 检查文件
- **解决**: 压缩文件或分割大文件

## 🚀 自动迁移

PyJianYin 提供了自动适配器，会：

1. **优先使用新 SDK**: 如果安装了 `google-genai`
2. **自动回退**: 如果新 SDK 不可用，使用旧 SDK
3. **统一接口**: 无论使用哪个 SDK，API 调用方式相同

## 📦 安装指南

### 推荐安装 (新 SDK)

```bash
# 安装新版 SDK
pip install google-genai>=1.0.0

# 更新项目依赖
pip install -r requirements.txt
```

### 备用安装 (旧 SDK)

```bash
# 如果新 SDK 不可用，使用旧版
pip install google-generativeai>=0.8.0
```

### 完整安装

```bash
# 安装所有依赖（包括可选依赖）
pip install -r requirements.txt
```

## 🔧 配置更新

### API Key 配置

API Key 配置方式保持不变：

```bash
# .env 文件
GEMINI_API_KEY=your_api_key_here
```

### 模型配置

新的默认模型配置：

```python
# config.py 中的更新
gemini_model_pro = "gemini-2.0-flash-exp"
gemini_model_flash = "gemini-2.0-flash"
```

## 🧪 测试新 API

运行 API 测试来验证配置：

```bash
# 测试 Gemini API 功能
python main.py test-api
```

测试会显示当前使用的 SDK 版本：

```
✅ 使用新版 Google GenAI SDK
📊 SDK 类型: google-genai
🤖 Pro 模型: gemini-2.0-flash-exp
```

## 🔍 故障排除

### 新 SDK 安装失败

**问题**: `pip install google-genai` 失败

**解决方案**:
1. 更新 pip: `pip install --upgrade pip`
2. 使用旧 SDK: `pip install google-generativeai>=0.8.0`
3. 检查 Python 版本 (需要 3.8+)

### API 调用失败

**问题**: 出现 "模型不存在" 错误

**解决方案**:
1. 检查 API Key 是否有效
2. 确认模型名称正确
3. 运行 `python main.py diagnose` 诊断

### 文件大小超过限制

**问题**: 文件超过 20MB 无法处理

**解决方案**:

#### 视频压缩
```bash
# 使用 FFmpeg 压缩视频
ffmpeg -i input.mp4 -crf 28 -preset medium output.mp4

# 压缩到指定大小
ffmpeg -i input.mp4 -fs 18M output.mp4

# 降低分辨率
ffmpeg -i input.mp4 -vf scale=1280:720 -crf 28 output.mp4
```

#### 图片压缩
```bash
# 使用 ImageMagick 压缩图片
magick input.jpg -quality 80 -resize 1920x1080 output.jpg

# 批量压缩
magick mogrify -quality 75 -resize 1920x1080 *.jpg
```

#### 在线工具
- **TinyPNG**: https://tinypng.com/ (图片压缩)
- **CloudConvert**: https://cloudconvert.com/ (视频压缩)
- **HandBrake**: 免费的视频压缩软件

### 性能问题

**问题**: API 调用变慢

**解决方案**:
1. 新模型可能有不同的性能特征
2. 调整批处理大小: `--batch-size 3`
3. 检查网络连接

## 📊 性能对比

### Gemini 2.0 vs 1.5

| 特性 | Gemini 1.5 | Gemini 2.0 |
|------|------------|------------|
| 响应速度 | 基准 | 更快 |
| 准确性 | 高 | 更高 |
| 多模态能力 | 支持 | 增强 |
| 上下文长度 | 1M tokens | 2M tokens |

### 实际测试结果

```bash
# 运行性能测试
python main.py test-api

# 预期结果
文字处理: 1-2秒 (vs 2-3秒)
图片分析: 3-5秒 (vs 5-8秒)
视频处理: 5-10秒 (vs 8-15秒)
```

## 🔄 代码迁移示例

### 旧代码 (不推荐)

```python
import google.generativeai as genai

genai.configure(api_key="your_key")
model = genai.GenerativeModel('gemini-1.5-pro-latest')
response = model.generate_content("Hello")
```

### 新代码 (推荐)

```python
from gemini_adapter import get_gemini_adapter

adapter = get_gemini_adapter()
response = adapter.generate_content("Hello")
```

### 适配器优势

- ✅ 自动选择最佳 SDK
- ✅ 统一的错误处理
- ✅ 向后兼容
- ✅ 自动重试机制

## 📅 迁移时间表

| 日期 | 事件 |
|------|------|
| 2024年12月 | 新 SDK 发布 |
| 2025年1月 | PyJianYin 支持新 SDK |
| 2025年3月 | 建议迁移到新 SDK |
| 2025年9月 | 旧 SDK 停止支持 |

## 🆘 获取帮助

### 自动诊断

```bash
# 运行完整诊断
python main.py diagnose

# 快速修复
python main.py fix

# API 功能测试
python main.py test-api
```

### 手动检查

```bash
# 检查已安装的包
pip list | grep -E "(google-genai|google-generativeai)"

# 测试适配器
python gemini_adapter.py
```

### 常见问题

**Q: 可以同时安装两个 SDK 吗？**
A: 可以，适配器会优先使用新 SDK。

**Q: 新 SDK 的 API 配额是否不同？**
A: 配额政策相同，但新模型可能有不同的计费方式。

**Q: 如何强制使用旧 SDK？**
A: 卸载新 SDK: `pip uninstall google-genai`

## 🎯 最佳实践

1. **优先使用新 SDK**: 获得最佳性能和功能
2. **保留旧 SDK**: 作为备用方案
3. **定期测试**: 运行 `python main.py test-api`
4. **监控性能**: 关注 API 调用时间和成功率
5. **及时更新**: 跟进最新的模型和功能

---

通过这个迁移指南，你可以顺利升级到最新的 Gemini API，享受更好的性能和功能。
