#!/usr/bin/env python3
"""
视频分割和分析模块
对于超过20MB的视频进行智能分割，分次解读后合并结果
"""

import os
import sys
import tempfile
import subprocess
from pathlib import Path
from typing import List, Dict, Optional, Tuple
from rich.console import Console
from rich.progress import Progress, SpinnerColumn, TextColumn, BarColumn, TaskProgressColumn
import json
import time

# 添加项目根目录到路径
sys.path.append(str(Path(__file__).parent))
from config import get_config

console = Console()
config = get_config()

class VideoSplitter:
    """视频分割器类"""
    
    def __init__(self):
        self.temp_dir = None
        self.segments = []
        
    def __enter__(self):
        """上下文管理器入口"""
        self.temp_dir = tempfile.mkdtemp(prefix="pyjianyin_video_")
        return self
        
    def __exit__(self, exc_type, exc_val, exc_tb):
        """上下文管理器出口，清理临时文件"""
        self.cleanup()
    
    def cleanup(self):
        """清理临时文件"""
        if self.temp_dir and Path(self.temp_dir).exists():
            try:
                import shutil
                shutil.rmtree(self.temp_dir)
                console.print(f"🧹 [dim]清理临时文件: {self.temp_dir}[/dim]")
            except Exception as e:
                console.print(f"⚠️ [yellow]清理临时文件失败: {e}[/yellow]")
    
    def get_video_info(self, video_path: str) -> Dict:
        """获取视频信息"""
        try:
            cmd = [
                'ffprobe', '-v', 'quiet', '-print_format', 'json',
                '-show_format', '-show_streams', video_path
            ]
            
            result = subprocess.run(cmd, capture_output=True, text=True, timeout=30)
            
            if result.returncode != 0:
                raise Exception(f"FFprobe 失败: {result.stderr}")
            
            info = json.loads(result.stdout)
            
            # 提取视频流信息
            video_stream = None
            for stream in info.get('streams', []):
                if stream.get('codec_type') == 'video':
                    video_stream = stream
                    break
            
            if not video_stream:
                raise Exception("未找到视频流")
            
            duration = float(info['format'].get('duration', 0))
            size_bytes = int(info['format'].get('size', 0))
            
            return {
                'duration': duration,
                'size_bytes': size_bytes,
                'size_mb': size_bytes / 1024 / 1024,
                'width': int(video_stream.get('width', 0)),
                'height': int(video_stream.get('height', 0)),
                'fps': eval(video_stream.get('r_frame_rate', '0/1')),
                'codec': video_stream.get('codec_name', 'unknown')
            }
            
        except subprocess.TimeoutExpired:
            raise Exception("获取视频信息超时")
        except FileNotFoundError:
            raise Exception("FFmpeg/FFprobe 未安装，请先安装 FFmpeg")
        except Exception as e:
            raise Exception(f"获取视频信息失败: {e}")
    
    def calculate_split_strategy(self, video_info: Dict, target_size_mb: float = 18.0) -> List[Dict]:
        """
        计算分割策略
        
        Args:
            video_info: 视频信息
            target_size_mb: 目标分段大小（MB）
            
        Returns:
            List[Dict]: 分割段信息列表
        """
        duration = video_info['duration']
        total_size_mb = video_info['size_mb']
        
        # 计算每MB对应的时长
        mb_per_second = total_size_mb / duration
        
        # 计算每段的目标时长
        target_duration = target_size_mb / mb_per_second
        
        # 确保每段至少5秒，最多300秒（5分钟）
        target_duration = max(5, min(target_duration, 300))
        
        # 计算分割点
        segments = []
        current_start = 0
        segment_index = 0
        
        while current_start < duration:
            segment_end = min(current_start + target_duration, duration)
            
            # 避免最后一段太短（少于3秒）
            if duration - segment_end < 3 and segment_end < duration:
                segment_end = duration
            
            segments.append({
                'index': segment_index,
                'start_time': current_start,
                'end_time': segment_end,
                'duration': segment_end - current_start,
                'estimated_size_mb': (segment_end - current_start) * mb_per_second
            })
            
            current_start = segment_end
            segment_index += 1
        
        return segments
    
    def split_video_segment(self, video_path: str, segment_info: Dict) -> str:
        """
        分割视频片段
        
        Args:
            video_path: 原视频路径
            segment_info: 分段信息
            
        Returns:
            str: 分割后的视频文件路径
        """
        segment_filename = f"segment_{segment_info['index']:03d}.mp4"
        segment_path = os.path.join(self.temp_dir, segment_filename)
        
        start_time = segment_info['start_time']
        duration = segment_info['duration']
        
        # 使用 FFmpeg 分割视频
        cmd = [
            'ffmpeg', '-y',  # 覆盖输出文件
            '-i', video_path,
            '-ss', str(start_time),  # 开始时间
            '-t', str(duration),     # 持续时间
            '-c', 'copy',            # 复制流，不重新编码（更快）
            '-avoid_negative_ts', 'make_zero',
            segment_path
        ]
        
        try:
            result = subprocess.run(
                cmd, 
                capture_output=True, 
                text=True, 
                timeout=60,
                cwd=self.temp_dir
            )
            
            if result.returncode != 0:
                raise Exception(f"FFmpeg 分割失败: {result.stderr}")
            
            # 验证输出文件
            if not Path(segment_path).exists():
                raise Exception("分割后的文件不存在")
            
            # 检查文件大小
            segment_size = Path(segment_path).stat().st_size
            segment_size_mb = segment_size / 1024 / 1024
            
            if segment_size_mb > 20:
                console.print(f"⚠️ [yellow]分段 {segment_info['index']} 仍超过20MB ({segment_size_mb:.1f}MB)[/yellow]")
            
            return segment_path
            
        except subprocess.TimeoutExpired:
            raise Exception(f"分割分段 {segment_info['index']} 超时")
        except Exception as e:
            raise Exception(f"分割分段 {segment_info['index']} 失败: {e}")
    
    def split_video(self, video_path: str) -> List[Dict]:
        """
        分割视频为多个片段
        
        Args:
            video_path: 视频文件路径
            
        Returns:
            List[Dict]: 分割后的片段信息列表
        """
        console.print(f"📹 [bold]开始分割视频: {Path(video_path).name}[/bold]")
        
        # 获取视频信息
        with Progress(
            SpinnerColumn(),
            TextColumn("[progress.description]{task.description}"),
            console=console
        ) as progress:
            
            task = progress.add_task("获取视频信息...", total=None)
            video_info = self.get_video_info(video_path)
            progress.remove_task(task)
        
        console.print(f"📊 视频信息: {video_info['duration']:.1f}秒, {video_info['size_mb']:.1f}MB")
        
        # 计算分割策略
        segments_plan = self.calculate_split_strategy(video_info)
        console.print(f"📋 分割计划: {len(segments_plan)} 个片段")
        
        # 执行分割
        segments_result = []
        
        with Progress(
            SpinnerColumn(),
            TextColumn("[progress.description]{task.description}"),
            BarColumn(),
            TaskProgressColumn(),
            console=console
        ) as progress:
            
            split_task = progress.add_task("分割视频片段...", total=len(segments_plan))
            
            for segment_info in segments_plan:
                try:
                    segment_path = self.split_video_segment(video_path, segment_info)
                    
                    # 获取实际文件大小
                    actual_size = Path(segment_path).stat().st_size
                    actual_size_mb = actual_size / 1024 / 1024
                    
                    segment_result = {
                        **segment_info,
                        'file_path': segment_path,
                        'actual_size_mb': actual_size_mb,
                        'within_limit': actual_size_mb <= 20
                    }
                    
                    segments_result.append(segment_result)
                    
                    progress.console.print(
                        f"  ✅ 片段 {segment_info['index']}: "
                        f"{segment_info['start_time']:.1f}s-{segment_info['end_time']:.1f}s "
                        f"({actual_size_mb:.1f}MB)"
                    )
                    
                except Exception as e:
                    progress.console.print(f"  ❌ 片段 {segment_info['index']} 失败: {e}")
                    # 继续处理其他片段
                
                progress.advance(split_task)
        
        self.segments = segments_result
        
        successful_segments = [s for s in segments_result if 'file_path' in s]
        console.print(f"✅ [green]成功分割 {len(successful_segments)}/{len(segments_plan)} 个片段[/green]")
        
        return successful_segments

def check_ffmpeg_availability() -> bool:
    """检查 FFmpeg 是否可用"""
    try:
        result = subprocess.run(['ffmpeg', '-version'], 
                              capture_output=True, 
                              timeout=10)
        return result.returncode == 0
    except (FileNotFoundError, subprocess.TimeoutExpired):
        return False

def estimate_split_count(video_path: str) -> Tuple[bool, int, str]:
    """
    估算视频是否需要分割以及分割数量
    
    Returns:
        Tuple[bool, int, str]: (是否需要分割, 预估分割数量, 描述信息)
    """
    try:
        file_size = Path(video_path).stat().st_size
        size_mb = file_size / 1024 / 1024
        
        if size_mb <= 20:
            return False, 1, f"{size_mb:.1f}MB (无需分割)"
        
        # 简单估算：假设均匀分布
        estimated_segments = max(2, int(size_mb / 18) + 1)
        
        return True, estimated_segments, f"{size_mb:.1f}MB (预估分割为 {estimated_segments} 段)"
        
    except Exception as e:
        return True, 0, f"无法获取文件信息: {e}"

def test_video_splitting():
    """测试视频分割功能"""
    console.print("[bold blue]测试视频分割功能[/bold blue]")
    
    # 检查 FFmpeg
    if not check_ffmpeg_availability():
        console.print("[red]ERROR:[/red] FFmpeg 未安装或不可用")
        console.print("[yellow]提示:[/yellow] 请安装 FFmpeg: https://ffmpeg.org/download.html")
        return False
    
    console.print("[green]SUCCESS:[/green] FFmpeg 可用")
    
    # 查找测试视频
    test_videos = []

    try:
        if not config.assets_dir.exists():
            console.print(f"⚠️ [yellow]素材目录不存在: {config.assets_dir}[/yellow]")
            console.print("💡 [yellow]请运行 'python main.py setup' 创建素材目录[/yellow]")
            return True

        for video_path in config.assets_dir.rglob("*.mp4"):
            if video_path.exists():
                file_size = video_path.stat().st_size / 1024 / 1024
                if file_size > 20:  # 只测试大于20MB的视频
                    test_videos.append(video_path)
    except Exception as e:
        console.print(f"❌ [red]扫描视频文件时出错: {e}[/red]")
        return False

    if not test_videos:
        console.print("⚠️ [yellow]未找到大于20MB的测试视频[/yellow]")
        console.print("💡 [yellow]请添加一些大视频文件到素材目录进行测试[/yellow]")
        return True
    
    # 测试第一个大视频
    test_video = test_videos[0]
    console.print(f"📹 测试视频: {test_video.name}")
    
    try:
        with VideoSplitter() as splitter:
            segments = splitter.split_video(str(test_video))
            
            if segments:
                console.print(f"✅ [green]分割测试成功: {len(segments)} 个片段[/green]")
                
                # 显示片段信息
                for segment in segments[:3]:  # 只显示前3个
                    console.print(f"  • 片段 {segment['index']}: "
                                f"{segment['duration']:.1f}s, "
                                f"{segment['actual_size_mb']:.1f}MB")
                
                if len(segments) > 3:
                    console.print(f"  • ... 还有 {len(segments) - 3} 个片段")
                
                return True
            else:
                console.print("❌ [red]分割测试失败[/red]")
                return False
                
    except Exception as e:
        console.print(f"❌ [red]分割测试异常: {e}[/red]")
        return False

if __name__ == "__main__":
    test_video_splitting()
