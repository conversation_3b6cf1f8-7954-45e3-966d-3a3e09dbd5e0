# Gemini API 测试指南

本指南介绍如何使用 PyJianYin 的 Gemini API 测试功能，帮助你验证 API 配置和网络连接是否正常。

## 🧪 API 测试功能

### 快速测试

运行完整的 API 功能测试：

```bash
python main.py test-api
```

这个命令会自动测试：
- ✅ 文字处理能力
- ✅ 图片分析功能  
- ✅ 视频处理功能
- ✅ API 响应速度

### 测试内容详解

#### 1. 文字处理测试

测试 Gemini 的文本生成和理解能力：

**测试项目**：
- 中文问答
- 英文翻译
- 概念解释
- JSON 格式输出

**预期结果**：
- 所有提示都能得到合理响应
- 响应时间在可接受范围内
- 输出格式正确

#### 2. 图片分析测试

测试 Gemini 的图像理解能力：

**测试流程**：
1. 创建或使用现有测试图片
2. 上传到 Gemini API
3. 进行多种类型的分析

**测试项目**：
- 图片内容描述
- 颜色识别
- JSON 格式分析
- 视频适用性评估

**预期结果**：
- 能够准确描述图片内容
- 正确识别主要颜色
- 输出结构化的分析结果

#### 3. 视频处理测试

测试 Gemini 的视频理解能力：

**测试流程**：
1. 创建简单测试视频或使用现有视频
2. 上传到 Gemini API
3. 分析视频内容

**测试项目**：
- 视频内容描述
- 动作识别
- 时长估计
- 结构化分析

**预期结果**：
- 能够理解视频内容
- 识别基本动作和变化
- 提供准确的时长信息

## 🔧 故障排除

### 常见问题

#### API Key 问题

**症状**：
```
❌ API Key 未配置，请先配置 GEMINI_API_KEY
```

**解决方案**：
1. 检查 `.env` 文件是否存在
2. 确认 API Key 格式正确
3. 运行 `python main.py fix` 进行修复

#### 网络连接问题

**症状**：
```
❌ 网络连接失败，无法访问 Gemini API
```

**解决方案**：
1. 检查网络连接
2. 运行 `python main.py diagnose` 诊断
3. 考虑使用 VPN 或代理

#### 文件上传失败

**症状**：
```
❌ 文件上传失败: FAILED
```

**解决方案**：
1. 检查文件大小（建议 < 50MB）
2. 确认文件格式支持
3. 检查网络稳定性

#### 依赖库缺失

**症状**：
```
⚠️ PIL 未安装，使用现有图片
⚠️ OpenCV 未安装，跳过视频测试
```

**解决方案**：
```bash
# 安装图片处理库
pip install Pillow

# 安装视频处理库
pip install opencv-python
```

### 测试结果解读

#### 全部通过
```
🎉 所有测试通过！(3/3)
✨ Gemini API 功能正常，可以正常使用 PyJianYin！
```

**说明**：API 配置正确，网络连接正常，可以开始使用 PyJianYin。

#### 部分失败
```
⚠️ 部分测试失败 (2/3)
🔧 请检查网络连接和 API 配置
```

**说明**：存在问题，需要根据具体失败项目进行排查。

#### 全部失败
```
⚠️ 部分测试失败 (0/3)
```

**说明**：API 配置或网络存在严重问题，需要全面检查。

## 🛠️ 高级用法

### 自定义测试

如果需要测试特定功能，可以直接运行测试脚本：

```bash
python test_gemini_api.py
```

### 使用现有素材测试

测试脚本会自动查找现有素材进行测试：

**图片测试**：
- 优先使用 `my_video_assets` 中的 PNG/JPG 文件
- 如果没有现有图片，会尝试创建测试图片

**视频测试**：
- 优先使用 `my_video_assets` 中小于 50MB 的 MP4/MOV 文件
- 如果没有合适视频，会尝试创建测试视频

### 批量测试

对于大量素材的测试，建议：

1. 先运行 API 测试确认基本功能
2. 使用小批量进行素材分析测试
3. 逐步增加批量大小

```bash
# 测试 API 基本功能
python main.py test-api

# 小批量测试素材分析
python main.py analyze --batch-size 3

# 如果成功，增加批量大小
python main.py analyze --batch-size 10
```

## 📊 性能基准

### 正常性能指标

**文字处理**：
- 响应时间：1-3 秒
- 成功率：> 95%

**图片分析**：
- 上传时间：2-10 秒（取决于文件大小）
- 分析时间：3-8 秒
- 成功率：> 90%

**视频处理**：
- 上传时间：5-30 秒（取决于文件大小）
- 分析时间：5-15 秒
- 成功率：> 85%

### 性能优化建议

1. **文件优化**：
   - 图片：< 5MB，推荐 1920x1080
   - 视频：< 50MB，推荐 1080p，时长 < 30秒

2. **网络优化**：
   - 使用稳定的网络连接
   - 避免网络高峰期
   - 考虑使用 CDN 或代理

3. **并发控制**：
   - 避免同时上传多个大文件
   - 使用适当的批处理大小
   - 监控 API 配额使用情况

## 🔍 调试技巧

### 启用详细日志

在测试脚本中添加调试信息：

```python
import logging
logging.basicConfig(level=logging.DEBUG)
```

### 检查 API 配额

访问 [Google AI Studio](https://makersuite.google.com/) 查看 API 使用情况。

### 网络抓包分析

使用网络分析工具检查请求和响应：

```bash
# 使用 curl 测试基本连接
curl -I https://generativelanguage.googleapis.com

# 检查 DNS 解析
nslookup generativelanguage.googleapis.com
```

## 📞 获取帮助

如果测试仍然失败，请：

1. 运行完整诊断：`python main.py diagnose`
2. 尝试快速修复：`python main.py fix`
3. 查看详细错误日志
4. 在 GitHub 上提交 Issue，包含：
   - 测试输出结果
   - 系统环境信息
   - 网络配置情况
   - 错误日志内容

---

通过这个测试指南，你应该能够全面验证 Gemini API 的功能，并快速定位和解决问题。
