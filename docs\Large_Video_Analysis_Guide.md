# 大视频分析指南

## 🎬 概述

PyJianYin 的大视频分析功能专门处理超过 20MB 的视频文件。通过智能分割、分次分析和结果合并，确保大视频也能获得完整、连贯的 AI 分析结果。

## 🔧 工作原理

### 1. 智能分割策略

```
原始大视频 (50MB, 120秒)
    ↓
自动分析视频信息
    ↓
计算最优分割点
    ↓
分割为多个片段 (每段 < 20MB)
```

**分割算法**:
- 根据视频码率计算每段目标时长
- 确保每段至少 5 秒，最多 5 分钟
- 避免最后一段过短（< 3 秒）
- 使用 FFmpeg 进行精确分割

### 2. 分次分析流程

```
片段1 [0-30s] → AI分析 → 结果1
    ↓ (上下文传递)
片段2 [30-60s] → AI分析 → 结果2
    ↓ (上下文传递)  
片段3 [60-90s] → AI分析 → 结果3
    ↓ (上下文传递)
片段4 [90-120s] → AI分析 → 结果4
```

**上下文保持**:
- 每次分析时传递前面片段的摘要
- 确保分析的连贯性和一致性
- 识别跨片段的转场和主题变化

### 3. 结果合并策略

```
多个片段结果
    ↓
时间轴重建
    ↓
标签去重和统计
    ↓
描述连接和整理
    ↓
完整的视频分析结果
```

## 🚀 使用方法

### 基本使用

```bash
# 1. 检查大视频文件
python main.py check-sizes

# 2. 测试分割功能
python main.py test-video-split

# 3. 测试完整分析
python main.py test-large-video

# 4. 正式分析素材
python main.py analyze
```

### 高级配置

在分析大视频时，系统会自动：
- 检测文件大小和格式
- 评估分割的必要性
- 选择最优的分割策略
- 执行分次分析
- 合并和优化结果

## 📊 分析结果格式

大视频分析的结果包含额外的元信息：

```json
{
  "description": "完整的视频描述，包含时间戳",
  "tags": ["合并后的标签列表"],
  "mood": "整体情绪",
  "category": "视频类别",
  "quality": "整体质量评估",
  "duration_category": "long",
  "total_duration": 120.5,
  "segment_count": 4,
  "analysis_method": "segmented_analysis",
  "segments_summary": [
    {
      "index": 0,
      "time_range": "0.0s-30.0s",
      "description": "第一段的描述摘要..."
    },
    {
      "index": 1,
      "time_range": "30.0s-60.0s", 
      "description": "第二段的描述摘要..."
    }
  ],
  "key_moments": ["重要时刻列表"]
}
```

## 🛠️ 依赖要求

### 必需依赖

1. **FFmpeg**: 用于视频分割
   ```bash
   # Windows (使用 Chocolatey)
   choco install ffmpeg
   
   # macOS (使用 Homebrew)
   brew install ffmpeg
   
   # Ubuntu/Debian
   sudo apt update && sudo apt install ffmpeg
   ```

2. **Google GenAI SDK**: 用于 AI 分析
   ```bash
   pip install google-genai>=1.0.0
   ```

### 检查依赖

```bash
# 检查 FFmpeg 是否安装
ffmpeg -version

# 检查 Python 依赖
python -c "from google import genai; print('✅ GenAI SDK 已安装')"
```

## 📈 性能优化

### 分割策略优化

- **目标片段大小**: 18MB (留有余量)
- **最小片段时长**: 5 秒
- **最大片段时长**: 300 秒 (5分钟)
- **最大片段数量**: 10 个 (超过则建议压缩)

### 分析效率

- **并发处理**: 目前为串行处理，确保上下文连贯
- **内存管理**: 分割后的片段不保存到磁盘
- **错误恢复**: 单个片段失败不影响其他片段

## 🔍 故障排除

### 常见问题

#### 1. FFmpeg 未安装
```
❌ FFmpeg 未安装，无法分割大视频
💡 请安装 FFmpeg 或压缩视频文件到20MB以下
```

**解决方案**:
- 安装 FFmpeg: https://ffmpeg.org/download.html
- 或压缩视频文件

#### 2. 视频过大
```
⚠️ 视频过大，预估需要分割为 15 段，建议先压缩
💡 可使用: ffmpeg -i input.mp4 -crf 28 -preset medium output.mp4
```

**解决方案**:
```bash
# 压缩视频
ffmpeg -i large_video.mp4 -crf 28 -preset medium compressed.mp4

# 或降低分辨率
ffmpeg -i large_video.mp4 -vf scale=1280:720 -crf 28 smaller.mp4
```

#### 3. 分割失败
```
❌ 分割分段 2 失败: FFmpeg 分割失败
```

**解决方案**:
- 检查视频文件是否损坏
- 尝试重新编码视频
- 检查磁盘空间

#### 4. 分析失败
```
❌ 分析片段 3 失败: API 调用失败
```

**解决方案**:
- 检查网络连接
- 验证 API Key
- 检查 API 配额

## 💡 最佳实践

### 1. 预处理建议

```bash
# 检查视频信息
ffprobe -v quiet -print_format json -show_format -show_streams video.mp4

# 预压缩大视频
ffmpeg -i input.mp4 -crf 28 -preset medium -movflags +faststart output.mp4
```

### 2. 批量处理

```bash
# 批量检查文件大小
python main.py check-sizes

# 批量分析（包含大视频）
python main.py analyze --batch-size 2
```

### 3. 质量控制

- 优先压缩而非分割（保持内容完整性）
- 分割时保持关键帧对齐
- 验证分析结果的连贯性

## 📋 示例工作流

### 完整示例

```bash
# 1. 初始化环境
python main.py setup

# 2. 检查文件大小
python main.py check-sizes

# 3. 测试大视频功能
python main.py test-large-video

# 4. 分析所有素材（包括大视频）
python main.py analyze

# 5. 查看分析结果
cat asset_manager/assets.json
```

### 预期输出

```
🎬 检测到大视频文件: large_video.mp4 (45.2MB)
📊 分割预估: 45.2MB (预估分割为 3 段)
📹 开始分割视频: large_video.mp4
📊 视频信息: 90.0秒, 45.2MB
📋 分割计划: 3 个片段
  ✅ 片段 0: 0.0s-30.0s (14.8MB)
  ✅ 片段 1: 30.0s-60.0s (15.1MB)
  ✅ 片段 2: 60.0s-90.0s (14.9MB)
✅ 成功分割 3/3 个片段

🔍 分析片段 0: 0.0s-30.0s
  ✅ 片段 0 分析完成
🔍 分析片段 1: 30.0s-60.0s
  ✅ 片段 1 分析完成
🔍 分析片段 2: 60.0s-90.0s
  ✅ 片段 2 分析完成

🔄 合并 3 个片段的分析结果...
✅ 大视频分析完成！
📊 总时长: 90.0秒
📋 片段数: 3
🏷️ 主要标签: technology, presentation, demo, professional, educational
```

## 🔗 相关文档

- [Gemini API 迁移指南](Gemini_API_Migration.md)
- [API 测试指南](API_Testing_Guide.md)
- [常见问题解答](FAQ.md)

---

通过大视频分析功能，PyJianYin 能够处理任意大小的视频文件，确保每个素材都能获得高质量的 AI 分析结果。
