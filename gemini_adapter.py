#!/usr/bin/env python3
"""
Gemini API 适配器
支持新旧两种 SDK，自动选择最佳的 API 调用方式
"""

import os
import sys
import time
from pathlib import Path
from typing import Optional, Dict, Any, Union
from rich.console import Console

# 添加项目根目录到路径
sys.path.append(str(Path(__file__).parent))
from config import get_config

console = Console()
config = get_config()

class GeminiAdapter:
    """Gemini API 适配器类"""
    
    def __init__(self, api_key: str = None):
        self.api_key = api_key or config.gemini_api_key
        self.client = None
        self.sdk_type = None
        self._initialize_client()
    
    def _initialize_client(self):
        """初始化客户端，优先使用新 SDK"""
        if not self.api_key or self.api_key == "YOUR_API_KEY":
            raise ValueError("API Key 未配置")
        
        # 尝试使用新的 google-genai SDK
        try:
            from google import genai
            from google.genai import types

            # 设置 API Key
            os.environ['GOOGLE_API_KEY'] = self.api_key
            self.client = genai.Client()
            self.types = types
            self.sdk_type = "google-genai"
            console.print("✅ [green]使用新版 Google GenAI SDK[/green]")
            return
        except ImportError:
            console.print("⚠️ [yellow]新版 Google GenAI SDK 未安装，尝试使用旧版[/yellow]")
        except Exception as e:
            console.print(f"⚠️ [yellow]新版 SDK 初始化失败: {e}[/yellow]")
        
        # 回退到旧的 google-generativeai SDK
        try:
            import google.generativeai as genai
            genai.configure(api_key=self.api_key)
            self.client = genai
            self.sdk_type = "google-generativeai"
            console.print("✅ [green]使用旧版 Google GenerativeAI SDK[/green]")
            return
        except ImportError:
            raise ImportError("未找到可用的 Gemini SDK，请安装 google-genai 或 google-generativeai")
        except Exception as e:
            raise Exception(f"SDK 初始化失败: {e}")
    
    def get_model(self, model_name: str = None):
        """获取模型实例"""
        if model_name is None:
            model_name = config.gemini_model_pro
        
        if self.sdk_type == "google-genai":
            # 新 SDK 使用方式
            return self.client.models.generate_content
        else:
            # 旧 SDK 使用方式
            return self.client.GenerativeModel(model_name)
    
    def generate_content(self, prompt: Union[str, list], model_name: str = None) -> Optional[str]:
        """
        生成内容的统一接口
        
        Args:
            prompt: 提示词或包含文件的列表
            model_name: 模型名称
            
        Returns:
            str: 生成的内容
        """
        try:
            if self.sdk_type == "google-genai":
                return self._generate_content_new_sdk(prompt, model_name)
            else:
                return self._generate_content_old_sdk(prompt, model_name)
        except Exception as e:
            console.print(f"❌ [red]内容生成失败: {e}[/red]")
            return None
    
    def _generate_content_new_sdk(self, prompt: Union[str, list], model_name: str = None) -> Optional[str]:
        """使用新 SDK 生成内容"""
        if model_name is None:
            model_name = config.gemini_model_pro

        # 确保模型名称格式正确
        if not model_name.startswith('models/'):
            model_name = f'models/{model_name}'

        try:
            if isinstance(prompt, str):
                # 纯文本提示
                response = self.client.models.generate_content(
                    model=model_name,
                    contents=prompt
                )
            else:
                # 包含文件的多模态提示
                parts = []
                for item in prompt:
                    if isinstance(item, str):
                        parts.append(self.types.Part(text=item))
                    else:
                        # 处理文件对象
                        if hasattr(item, 'data') and hasattr(item, 'mime_type'):
                            # 直接使用文件数据
                            parts.append(self.types.Part(
                                inline_data=self.types.Blob(
                                    data=item.data,
                                    mime_type=item.mime_type
                                )
                            ))
                        elif hasattr(item, 'path'):
                            # 读取文件数据
                            with open(item.path, 'rb') as f:
                                file_data = f.read()
                            mime_type = self._get_mime_type(item.path)
                            parts.append(self.types.Part(
                                inline_data=self.types.Blob(
                                    data=file_data,
                                    mime_type=mime_type
                                )
                            ))

                content = self.types.Content(parts=parts)
                response = self.client.models.generate_content(
                    model=model_name,
                    contents=content
                )

            return response.text if hasattr(response, 'text') else str(response)

        except Exception as e:
            console.print(f"❌ [red]新 SDK 生成失败: {e}[/red]")
            return None
    
    def _generate_content_old_sdk(self, prompt: Union[str, list], model_name: str = None) -> Optional[str]:
        """使用旧 SDK 生成内容"""
        if model_name is None:
            model_name = config.gemini_model_pro
        
        try:
            model = self.client.GenerativeModel(model_name)
            response = model.generate_content(prompt)
            return response.text if hasattr(response, 'text') else str(response)
            
        except Exception as e:
            console.print(f"❌ [red]旧 SDK 生成失败: {e}[/red]")
            return None
    
    def upload_file(self, file_path: str) -> Optional[Any]:
        """
        上传文件的统一接口
        
        Args:
            file_path: 文件路径
            
        Returns:
            文件对象
        """
        try:
            if self.sdk_type == "google-genai":
                return self._upload_file_new_sdk(file_path)
            else:
                return self._upload_file_old_sdk(file_path)
        except Exception as e:
            console.print(f"❌ [red]文件上传失败: {e}[/red]")
            return None
    
    def _upload_file_new_sdk(self, file_path: str) -> Optional[Any]:
        """使用新 SDK 处理文件（直接读取，不上传）"""
        try:
            # 新 SDK 使用直接读取文件的方式，不需要上传
            with open(file_path, 'rb') as f:
                file_data = f.read()

            # 根据文件类型确定 MIME 类型
            mime_type = self._get_mime_type(file_path)

            # 创建一个包含文件数据的对象
            class FileData:
                def __init__(self, data, mime_type, path):
                    self.data = data
                    self.mime_type = mime_type
                    self.path = path

            return FileData(file_data, mime_type, file_path)

        except Exception as e:
            console.print(f"❌ [red]新 SDK 文件处理失败: {e}[/red]")
            return None
    
    def _upload_file_old_sdk(self, file_path: str) -> Optional[Any]:
        """使用旧 SDK 上传文件"""
        try:
            uploaded_file = self.client.upload_file(path=file_path)
            
            # 等待文件处理完成
            while uploaded_file.state.name == "PROCESSING":
                time.sleep(1)
                uploaded_file = self.client.get_file(uploaded_file.name)
            
            return uploaded_file
            
        except Exception as e:
            console.print(f"❌ [red]旧 SDK 文件上传失败: {e}[/red]")
            return None
    
    def delete_file(self, file_obj: Any) -> bool:
        """删除上传的文件"""
        try:
            if self.sdk_type == "google-genai":
                # 新 SDK 不需要删除文件，因为没有上传到服务器
                return True
            else:
                self.client.delete_file(file_obj.name)
            return True
        except Exception as e:
            console.print(f"⚠️ [yellow]文件删除失败: {e}[/yellow]")
            return False
    
    def _get_mime_type(self, file_path: str) -> str:
        """根据文件扩展名获取 MIME 类型"""
        ext = Path(file_path).suffix.lower()
        mime_types = {
            '.png': 'image/png',
            '.jpg': 'image/jpeg',
            '.jpeg': 'image/jpeg',
            '.gif': 'image/gif',
            '.bmp': 'image/bmp',
            '.mp4': 'video/mp4',
            '.mov': 'video/quicktime',
            '.avi': 'video/x-msvideo',
            '.mkv': 'video/x-matroska',
            '.mp3': 'audio/mpeg',
            '.wav': 'audio/wav',
            '.txt': 'text/plain',
        }
        return mime_types.get(ext, 'application/octet-stream')
    
    def get_sdk_info(self) -> Dict[str, str]:
        """获取当前使用的 SDK 信息"""
        return {
            "sdk_type": self.sdk_type,
            "model_pro": config.gemini_model_pro,
            "model_flash": config.gemini_model_flash,
            "api_key_configured": bool(self.api_key and self.api_key != "YOUR_API_KEY")
        }

# 全局适配器实例
_adapter = None

def get_gemini_adapter() -> GeminiAdapter:
    """获取全局 Gemini 适配器实例"""
    global _adapter
    if _adapter is None:
        _adapter = GeminiAdapter()
    return _adapter

def test_adapter():
    """测试适配器功能"""
    console.print("🧪 [bold blue]测试 Gemini 适配器[/bold blue]")
    
    try:
        adapter = get_gemini_adapter()
        info = adapter.get_sdk_info()
        
        console.print(f"📊 SDK 类型: {info['sdk_type']}")
        console.print(f"🤖 Pro 模型: {info['model_pro']}")
        console.print(f"⚡ Flash 模型: {info['model_flash']}")
        console.print(f"🔑 API Key: {'已配置' if info['api_key_configured'] else '未配置'}")
        
        # 测试简单的文本生成
        response = adapter.generate_content("Hello, this is a test.")
        if response:
            console.print(f"✅ [green]文本生成测试成功[/green]")
            console.print(f"📝 响应: {response[:100]}...")
        else:
            console.print("❌ [red]文本生成测试失败[/red]")
        
        return True
        
    except Exception as e:
        console.print(f"❌ [red]适配器测试失败: {e}[/red]")
        return False

if __name__ == "__main__":
    test_adapter()
