#!/usr/bin/env python3
"""
基本功能测试脚本
测试核心功能而不依赖大视频文件
"""

import os
import sys
from pathlib import Path
from rich.console import Console
from rich.panel import Panel

# 添加项目根目录到路径
sys.path.append(str(Path(__file__).parent))
from config import get_config

console = Console()
config = get_config()

def test_environment_setup():
    """测试环境设置"""
    console.print("🔧 [bold]测试环境设置...[/bold]")
    
    try:
        # 检查 API Key
        if not config.validate_api_key():
            console.print("❌ API Key 未配置")
            return False
        console.print("✅ API Key 已配置")
        
        # 检查素材目录
        if not config.assets_dir.exists():
            console.print(f"⚠️ 素材目录不存在，创建: {config.assets_dir}")
            config.assets_dir.mkdir(parents=True, exist_ok=True)
        console.print(f"✅ 素材目录: {config.assets_dir}")
        
        return True
        
    except Exception as e:
        console.print(f"❌ 环境设置测试失败: {e}")
        return False

def test_gemini_sdk():
    """测试 Gemini SDK"""
    console.print("🤖 [bold]测试 Gemini SDK...[/bold]")
    
    try:
        from google import genai
        from google.genai import types
        
        # 设置 API Key
        os.environ['GOOGLE_API_KEY'] = config.gemini_api_key
        client = genai.Client()
        
        # 简单的文本测试
        response = client.models.generate_content(
            model='models/gemini-2.5-flash',
            contents="Hello, this is a test. Please respond with 'Test successful'."
        )
        
        if response and response.text and "successful" in response.text.lower():
            console.print("✅ Gemini SDK 工作正常")
            return True
        else:
            console.print("❌ Gemini SDK 响应异常")
            return False
            
    except ImportError:
        console.print("❌ Google GenAI SDK 未安装")
        return False
    except Exception as e:
        console.print(f"❌ Gemini SDK 测试失败: {e}")
        return False

def test_ffmpeg():
    """测试 FFmpeg"""
    console.print("🎬 [bold]测试 FFmpeg...[/bold]")
    
    try:
        import subprocess
        result = subprocess.run(['ffmpeg', '-version'], 
                              capture_output=True, 
                              timeout=10)
        
        if result.returncode == 0:
            console.print("✅ FFmpeg 可用")
            return True
        else:
            console.print("❌ FFmpeg 不可用")
            return False
            
    except FileNotFoundError:
        console.print("❌ FFmpeg 未安装")
        return False
    except Exception as e:
        console.print(f"❌ FFmpeg 测试失败: {e}")
        return False

def test_file_operations():
    """测试文件操作"""
    console.print("📁 [bold]测试文件操作...[/bold]")
    
    try:
        # 测试创建临时文件
        import tempfile
        with tempfile.NamedTemporaryFile(suffix='.txt', delete=False) as f:
            f.write(b"test content")
            temp_path = f.name
        
        # 测试文件大小检查
        size_ok, size_info = config.check_file_size(temp_path)
        console.print(f"✅ 文件大小检查: {size_info}")
        
        # 清理
        os.unlink(temp_path)
        
        return True
        
    except Exception as e:
        console.print(f"❌ 文件操作测试失败: {e}")
        return False

def test_config_validation():
    """测试配置验证"""
    console.print("⚙️ [bold]测试配置验证...[/bold]")
    
    try:
        # 测试支持的文件格式
        test_files = [
            "test.mp4",
            "test.jpg", 
            "test.png",
            "test.mp3",
            "test.txt"  # 不支持的格式
        ]
        
        for file_name in test_files:
            is_supported = config.is_supported_file(file_name)
            expected = file_name.endswith('.txt') == False
            
            if is_supported == expected:
                console.print(f"✅ {file_name}: {'支持' if is_supported else '不支持'}")
            else:
                console.print(f"❌ {file_name}: 检测结果错误")
                return False
        
        return True
        
    except Exception as e:
        console.print(f"❌ 配置验证测试失败: {e}")
        return False

def create_sample_assets():
    """创建示例素材文件"""
    console.print("📝 [bold]创建示例素材...[/bold]")
    
    try:
        # 创建示例文本文件（模拟小素材）
        sample_dir = config.assets_dir / "samples"
        sample_dir.mkdir(exist_ok=True)
        
        # 创建一个小的示例文件
        sample_file = sample_dir / "sample.txt"
        with open(sample_file, 'w', encoding='utf-8') as f:
            f.write("这是一个示例素材文件\n")
            f.write("用于测试文件扫描功能\n")
            f.write("文件大小很小，符合 API 限制\n")
        
        console.print(f"✅ 创建示例文件: {sample_file}")
        
        # 检查文件
        if sample_file.exists():
            size_ok, size_info = config.check_file_size(str(sample_file))
            console.print(f"📊 文件大小: {size_info}")
            return True
        else:
            console.print("❌ 示例文件创建失败")
            return False
            
    except Exception as e:
        console.print(f"❌ 创建示例素材失败: {e}")
        return False

def main():
    """主测试函数"""
    console.print(Panel(
        "[bold blue]PyJianYin 基本功能测试[/bold blue]\n" +
        "测试核心功能和依赖项",
        border_style="blue"
    ))
    
    # 测试项目
    tests = [
        ("环境设置", test_environment_setup),
        ("Gemini SDK", test_gemini_sdk),
        ("FFmpeg", test_ffmpeg),
        ("文件操作", test_file_operations),
        ("配置验证", test_config_validation),
        ("示例素材", create_sample_assets)
    ]
    
    results = {}
    
    for test_name, test_func in tests:
        console.print(f"\n{'='*50}")
        console.print(f"🧪 [bold]测试: {test_name}[/bold]")
        console.print('='*50)
        
        try:
            results[test_name] = test_func()
        except Exception as e:
            console.print(f"❌ [red]{test_name} 测试异常: {e}[/red]")
            results[test_name] = False
    
    # 显示结果
    console.print(f"\n{'='*50}")
    console.print("📊 [bold]测试结果总结[/bold]")
    console.print('='*50)
    
    passed_tests = sum(results.values())
    total_tests = len(results)
    
    for test_name, success in results.items():
        status = "✅ 通过" if success else "❌ 失败"
        console.print(f"  • {test_name}: {status}")
    
    # 总结
    if passed_tests == total_tests:
        console.print(f"\n🎉 [bold green]所有基本功能测试通过！({passed_tests}/{total_tests})[/bold green]")
        console.print("✨ [green]PyJianYin 核心功能正常[/green]")
        
        console.print(f"\n📋 [bold]下一步建议:[/bold]")
        console.print("  1. 添加素材文件到 my_video_assets 目录")
        console.print("  2. 运行 'python main.py analyze' 分析素材")
        console.print("  3. 如有大视频文件，系统会自动处理")
        
    else:
        console.print(f"\n⚠️ [bold yellow]部分测试失败 ({passed_tests}/{total_tests})[/bold yellow]")
        
        failed_tests = [name for name, success in results.items() if not success]
        console.print(f"\n❌ [bold]失败的测试:[/bold]")
        for test_name in failed_tests:
            console.print(f"  • {test_name}")
        
        console.print(f"\n🔧 [bold]修复建议:[/bold]")
        if "Gemini SDK" in failed_tests:
            console.print("  • 检查网络连接和 API Key")
        if "FFmpeg" in failed_tests:
            console.print("  • 安装 FFmpeg: https://ffmpeg.org/download.html")
        if "环境设置" in failed_tests:
            console.print("  • 检查文件权限和磁盘空间")
    
    return passed_tests == total_tests

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
