#!/usr/bin/env python3
"""
控制台输出工具
处理 Unicode 字符兼容性问题
"""

import sys
import os
from rich.console import Console

# 创建一个 Unicode 安全的控制台
def create_safe_console():
    """创建一个 Unicode 安全的控制台"""
    try:
        # 尝试设置控制台编码
        if sys.platform == "win32":
            # Windows 系统特殊处理
            os.system("chcp 65001 > nul 2>&1")  # 设置为 UTF-8
        
        # 创建控制台，禁用 emoji 如果不支持
        console = Console(
            force_terminal=True,
            legacy_windows=True if sys.platform == "win32" else False
        )
        
        # 测试 Unicode 支持
        try:
            console.print("🧪", end="")
            return console, True  # 支持 emoji
        except UnicodeEncodeError:
            return console, False  # 不支持 emoji
            
    except Exception:
        # 回退到基本控制台
        return Console(legacy_windows=True), False

# 全局控制台实例
safe_console, emoji_support = create_safe_console()

def safe_print(text: str, style: str = None, emoji_fallback: str = None):
    """
    安全的打印函数，自动处理 emoji 兼容性
    
    Args:
        text: 要打印的文本
        style: Rich 样式
        emoji_fallback: emoji 不支持时的替代文本
    """
    if not emoji_support and emoji_fallback:
        text = emoji_fallback
    
    if style:
        safe_console.print(f"[{style}]{text}[/{style}]")
    else:
        safe_console.print(text)

def safe_status_print(status: str, message: str, status_style: str = None):
    """
    安全的状态打印函数
    
    Args:
        status: 状态标识（如 "SUCCESS", "ERROR", "WARNING"）
        message: 消息内容
        status_style: 状态的样式
    """
    if status_style:
        safe_console.print(f"[{status_style}]{status}:[/{status_style}] {message}")
    else:
        safe_console.print(f"{status}: {message}")

# 预定义的状态打印函数
def print_success(message: str):
    """打印成功消息"""
    if emoji_support:
        safe_console.print(f"✅ [green]{message}[/green]")
    else:
        safe_console.print(f"[green]SUCCESS:[/green] {message}")

def print_error(message: str):
    """打印错误消息"""
    if emoji_support:
        safe_console.print(f"❌ [red]{message}[/red]")
    else:
        safe_console.print(f"[red]ERROR:[/red] {message}")

def print_warning(message: str):
    """打印警告消息"""
    if emoji_support:
        safe_console.print(f"⚠️ [yellow]{message}[/yellow]")
    else:
        safe_console.print(f"[yellow]WARNING:[/yellow] {message}")

def print_info(message: str):
    """打印信息消息"""
    if emoji_support:
        safe_console.print(f"💡 [blue]{message}[/blue]")
    else:
        safe_console.print(f"[blue]INFO:[/blue] {message}")

def print_progress(message: str):
    """打印进度消息"""
    if emoji_support:
        safe_console.print(f"🔄 [cyan]{message}[/cyan]")
    else:
        safe_console.print(f"[cyan]PROGRESS:[/cyan] {message}")

def print_test(message: str):
    """打印测试消息"""
    if emoji_support:
        safe_console.print(f"🧪 [bold blue]{message}[/bold blue]")
    else:
        safe_console.print(f"[bold blue]TEST:[/bold blue] {message}")

# 导出控制台实例供其他模块使用
__all__ = [
    'safe_console', 
    'emoji_support',
    'safe_print',
    'safe_status_print',
    'print_success',
    'print_error', 
    'print_warning',
    'print_info',
    'print_progress',
    'print_test'
]
