# PyJianYin - AI 驱动的自动视频生成系统

[![Python Version](https://img.shields.io/badge/python-3.10+-blue.svg)](https://python.org)
[![License](https://img.shields.io/badge/license-MIT-green.svg)](LICENSE)
[![Status](https://img.shields.io/badge/status-alpha-orange.svg)](https://github.com/your-username/pyjianyin)

> 🚀 让 Gemini AI 成为你的视频创作助手，自动生成高质量的剪映草稿！

PyJianYin 是一个基于 **Gemini AI** 和 **pyJianYingDraft** 的自动视频生成系统。它将 Gemini 大模型作为"创意大脑"，pyJianYingDraft 作为"执行手臂"，构建了一个全自动的视频生成流水线。

## ✨ 核心特性

- 🤖 **AI 驱动**: 使用 Gemini 2.5 Pro 智能分析素材和生成剧本
- 🎬 **多种模式**: 支持知识科普、经济解读、搞笑卡点、主流视频二创等模式
- 📊 **智能匹配**: 基于标签、分类、情绪的智能素材匹配算法
- 🎵 **音乐同步**: 自动分析音乐节拍，实现精准卡点
- 🎨 **丰富特效**: 支持转场、滤镜、关键帧动画等剪辑特效
- 📝 **字幕集成**: 自动导入 SRT 字幕，支持多种样式
- 🖥️ **友好界面**: 提供命令行界面和一键式操作

## 🏗️ 系统架构

PyJianYin 采用模块化设计，包含四个核心模块：

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   资产库模块    │───▶│  剧本生成器     │───▶│  草稿构建器     │───▶│  导出控制器     │
│ Asset Library   │    │ Storyboard Gen  │    │ Draft Builder   │    │Export Controller│
└─────────────────┘    └─────────────────┘    └─────────────────┘    └─────────────────┘
│                      │                      │                      │
│ • 素材分析            │ • AI 剧本生成         │ • 剪映草稿构建       │ • 自动导出
│ • 标签提取            │ • 多模式支持          │ • 智能素材匹配       │ • 批量处理
│ • 元数据管理          │ • 音乐节拍分析        │ • 特效添加           │ • 质量控制
└──────────────────────┴──────────────────────┴──────────────────────┴──────────────────
```

## 🚀 快速开始

### 1. 环境准备

```bash
# 克隆项目
git clone https://github.com/your-username/pyjianyin.git
cd pyjianyin

# 安装依赖 (推荐使用新版 Gemini SDK)
pip install google-genai>=1.0.0
pip install -r requirements.txt

# 初始化环境
python main.py setup
```

> 💡 **重要更新**: PyJianYin 现在支持最新的 Google GenAI SDK (Gemini 2.5)，提供更好的性能和功能。详见 [迁移指南](docs/Gemini_API_Migration.md)。

### 2. 配置 API Key

```bash
# 复制配置模板
cp .env.example .env

# 编辑配置文件，填入你的 Gemini API Key
# 在 https://makersuite.google.com/app/apikey 获取 API Key
```

### 3. 准备素材

将你的视频、图片、音乐文件放入 `my_video_assets` 目录：

```
my_video_assets/
├── videos/
│   ├── knowledge/     # 知识科普类视频
│   ├── economics/     # 经济相关视频
│   └── funny/         # 搞笑娱乐视频
├── images/
│   ├── charts/        # 图表数据
│   └── backgrounds/   # 背景图片
└── music/             # 背景音乐
```

### 4. 一键生成视频

```bash
# 一键自动生成（推荐）
python main.py auto

# 或者分步执行
python main.py analyze          # 分析素材
python main.py storyboard       # 生成剧本
python main.py build           # 构建草稿
python main.py export          # 导出视频
```

## 📖 详细使用指南

### 命令行界面

PyJianYin 提供了丰富的命令行选项：

```bash
# 查看帮助
python main.py --help

# 查看项目状态
python main.py status

# 测试 Gemini API 功能
python main.py test-api

# 测试新版 Google GenAI SDK
python main.py test-new-api

# 检查文件大小是否符合 API 限制
python main.py check-sizes

# 测试视频分割功能
python main.py test-video-split

# 测试大视频分析功能
python main.py test-large-video

# 修复 Unicode 兼容性问题（Windows 用户推荐）
python main.py fix-unicode

# 测试 Unicode 安全功能（推荐先运行）
python main.py test-safe

# 测试基本功能
python main.py test-basic

# 测试完整工作流程
python main.py test-workflow

# 分析素材库
python main.py analyze --force  # 强制重新分析

# 生成剧本
python main.py storyboard \
  --theme "人工智能的发展" \
  --mode "知识科普" \
  --srt "examples/example.srt" \
  --music "examples/example.mp3"

# 构建草稿
python main.py build \
  --storyboard "storyboard.json" \
  --mode "知识科普" \
  --output "my_draft.json"

# 导出视频
python main.py export \
  --draft "my_draft.json" \
  --output "output/my_video.mp4"
```

### 视频模式详解

#### 🎓 知识科普模式
- **特点**: 节奏平稳，重视信息传达
- **适用**: 教育内容、技术讲解、科学普及
- **转场**: 叠化、淡入淡出、闪白
- **素材**: 偏好 technology、science、education 标签

#### 💰 经济解读模式
- **特点**: 严肃专业，数据驱动
- **适用**: 财经分析、市场解读、商业内容
- **转场**: 叠化、推拉、擦除
- **素材**: 偏好 economics、business、data 标签

#### 😄 搞笑卡点模式
- **特点**: 快节奏，配合音乐节拍
- **适用**: 娱乐内容、搞笑视频、网络梗
- **转场**: 信号故障、抖动、闪烁、缩放
- **素材**: 偏好 funny、meme、entertainment 标签

#### 🎬 主流视频二创模式
- **特点**: 基于现有视频的二次创作
- **适用**: 影视解说、精彩剪辑、混剪视频
- **转场**: 叠化、推拉、旋转
- **素材**: 偏好 highlight、dramatic 标签

### 素材管理

#### 素材分类建议

```
my_video_assets/
├── videos/
│   ├── knowledge/      # 科学实验、技术演示、教育场景
│   ├── economics/      # 股市图表、商务会议、工厂生产
│   ├── funny/          # 搞笑动物、意外瞬间、表情包
│   ├── technology/     # 电子产品、编程代码、机器人
│   ├── nature/         # 风景延时、动物世界、自然美景
│   └── business/       # 会议场景、团队协作、商务活动
├── images/
│   ├── charts/         # 数据图表、信息图表
│   ├── backgrounds/    # 纯色背景、纹理背景
│   ├── icons/          # 图标素材、符号
│   └── memes/          # 表情包、网络梗图
└── music/
    ├── calm/           # 平静音乐（60-100 BPM）
    ├── energetic/      # 活力音乐（120-160 BPM）
    └── serious/        # 严肃音乐（80-110 BPM）
```

#### 素材要求

- **视频**: MP4/MOV/AVI 格式，建议 1080p，5-30秒时长
- **图片**: JPG/PNG 格式，建议 1920x1080 分辨率
- **音乐**: MP3/WAV 格式，建议 320kbps，1-5分钟时长
- **文件大小**: 单个文件不超过 **20MB** (新 API 限制)
- **命名**: 使用英文，避免特殊字符和空格

> ⚠️ **重要**: 新版 Gemini API 对单个文件有 20MB 的大小限制。使用 `python main.py check-sizes` 检查你的素材文件。

> 🎬 **大视频处理**: 对于超过 20MB 的视频文件，PyJianYin 会自动分割并分次分析，确保解读的连贯性和完整性。

### 字幕文件格式

支持标准 SRT 格式：

```srt
1
00:00:00,000 --> 00:00:03,500
人工智能正在改变我们的工作方式

2
00:00:03,500 --> 00:00:07,000
从自动化生产线到智能客服系统
```

## 🔧 高级配置

### 环境变量配置

在 `.env` 文件中可以配置以下选项：

```env
# Gemini AI API 配置
GEMINI_API_KEY=your_api_key_here

# 默认设置
DEFAULT_VIDEO_MODE=知识科普
DEFAULT_RESOLUTION=1080p
DEFAULT_FRAMERATE=30
DEFAULT_QUALITY=high
```

### 自定义配置

可以通过修改 `config.py` 来自定义：

- 支持的文件格式
- 视频模式配置
- 导出设置
- 路径配置

### API 使用示例

除了命令行界面，你也可以直接在代码中使用各个模块：

```python
from asset_manager.asset_analyzer import analyze_all_assets
from storyboard_generator.storyboard_generator import generate_storyboard
from draft_builder.draft_builder import build_draft_from_storyboard

# 分析素材
analyze_all_assets("my_video_assets", "assets.json")

# 生成剧本
generate_storyboard(
    theme="人工智能的发展",
    mode="知识科普",
    assets_json="assets.json",
    srt_path="example.srt",
    music_path="example.mp3",
    output_json="storyboard.json"
)

# 构建草稿
build_draft_from_storyboard(
    storyboard_json_path="storyboard.json",
    assets_json_path="assets.json",
    music_path="example.mp3",
    srt_path="example.srt",
    output_draft_path="draft.json",
    mode="知识科普"
)
```

## 🔧 网络故障排除

### 网络连接问题诊断

如果遇到网络连接问题，请按以下步骤排查：

#### 1. 测试 Gemini API 功能
```bash
python main.py test-api
```

这会测试：
- 文字处理能力
- 图片分析功能
- 视频处理功能
- API 响应速度

#### 2. 运行网络诊断
```bash
python main.py diagnose
```

这会检查：
- 基本网络连接
- DNS 解析
- Gemini API 访问
- 文件上传功能

#### 2. 常见网络错误

**错误 10060 - 连接超时**：
```bash
# 解决方案
python main.py test-api                       # 先测试 API 功能
python main.py analyze --skip-network-check  # 跳过网络检查
python main.py offline                        # 使用离线模式
```

**DNS 解析失败**：
- 更换 DNS 服务器（如 *******）
- 清除 DNS 缓存：`ipconfig /flushdns`

**API 访问被阻止**：
- 检查防火墙设置
- 使用 VPN 或代理
- 确认所在地区支持 Gemini API

#### 3. 离线模式

当网络不稳定时，可以使用离线模式：

```bash
python main.py offline    # 设置离线模式
python main.py build      # 构建草稿（使用基础素材信息）
```

离线模式特点：
- 基于文件路径推断素材属性
- 使用规则生成简单剧本
- 不依赖网络连接
- 准确性较低，建议网络恢复后重新分析

#### 4. 网络优化建议

**文件优化**：
- 单个文件 < 50MB
- 使用常见格式（MP4, JPG, PNG）
- 避免特殊字符的文件名

**网络优化**：
- 使用有线网络而非 WiFi
- 避免网络高峰期
- 关闭其他占用带宽的应用

**批处理优化**：
```bash
# 分批处理，减少并发
python main.py analyze --batch-size 5
```

## 🐛 常见问题

### Q: 如何获取 Gemini API Key？
A: 访问 [Google AI Studio](https://makersuite.google.com/app/apikey)，登录后创建新的 API Key。

### Q: 新版 Google GenAI SDK 有什么优势？
A:
- **更快的响应速度**: Gemini 2.5 模型性能提升
- **更好的准确性**: 改进的 AI 分析能力
- **统一的 API**: 更一致的调用方式
- **向前兼容**: 支持最新功能和模型

详见 [Gemini API 迁移指南](docs/Gemini_API_Migration.md)。

### Q: 为什么有 20MB 的文件大小限制？
A: 这是新版 Gemini API 的技术限制。解决方案：
- **视频压缩**: 使用 FFmpeg 或 HandBrake 压缩视频
- **图片压缩**: 降低分辨率或质量
- **文件分割**: 将长视频分割成多个片段
- **检查工具**: 运行 `python main.py check-sizes` 检查文件大小

### Q: 如何处理超过 20MB 的大视频？
A: PyJianYin 提供自动分割分析功能：
- **自动分割**: 智能分割大视频为多个片段
- **分次分析**: 对每个片段进行 AI 分析
- **结果合并**: 保持分析的连贯性和时序
- **无需保存**: 分割后的片段不会保存到磁盘

```bash
# 测试大视频分析功能
python main.py test-large-video

# 直接分析（会自动处理大视频）
python main.py analyze
```

### Q: 如何压缩视频文件？
A: 推荐使用 FFmpeg：
```bash
# 压缩视频（保持质量）
ffmpeg -i input.mp4 -crf 28 -preset medium output.mp4

# 压缩到指定大小（如 15MB）
ffmpeg -i input.mp4 -fs 15M output.mp4
```

### Q: 支持哪些视频格式？
A: 目前支持 MP4、MOV、AVI、MKV、WMV 格式的视频文件。

### Q: 为什么素材分析很慢或失败？
A: 素材分析需要上传文件到 Gemini API 进行 AI 分析，可能遇到网络问题。解决方案：

**网络优化**：
- 使用稳定的网络连接
- 避免在网络高峰期进行分析
- 考虑使用 VPN 或代理

**文件优化**：
- 控制单个文件在 50MB 以内
- 使用常见的文件格式
- 压缩过大的文件

**工具使用**：
- 运行 `python main.py diagnose` 进行网络诊断
- 使用 `--skip-network-check` 跳过网络检查
- 网络不稳定时使用 `python main.py offline` 离线模式

### Q: 如何提高素材匹配的准确性？
A: 建议：
- 使用描述性的文件名
- 按类别组织文件夹结构
- 准备多样化的素材
- 在剧本中使用精确的标签

### Q: 导出功能不工作怎么办？
A: 导出功能依赖剪映软件，请确保：
- 已安装剪映 6.0 或更低版本
- 剪映已打开并停留在主页
- 使用 Windows 系统
- 关闭其他可能干扰的软件

## 🚀 性能优化

### 素材库优化
- 定期清理不需要的素材
- 使用合适的文件格式和大小
- 按类别组织文件结构

### 生成速度优化
- 使用 `--skip-analyze` 跳过重复的素材分析
- 准备高质量的字幕文件
- 选择合适的音乐文件

### 内存使用优化
- 避免同时处理过多大文件
- 定期清理临时文件
- 监控系统资源使用情况

## 🤝 贡献指南

我们欢迎任何形式的贡献！

### 开发环境设置

```bash
# 克隆项目
git clone https://github.com/your-username/pyjianyin.git
cd pyjianyin

# 创建虚拟环境
python -m venv venv
source venv/bin/activate  # Windows: venv\Scripts\activate

# 安装开发依赖
pip install -r requirements.txt
pip install -e .

# 运行测试
python -m pytest tests/
```

### 贡献流程

1. Fork 项目
2. 创建特性分支 (`git checkout -b feature/AmazingFeature`)
3. 提交更改 (`git commit -m 'Add some AmazingFeature'`)
4. 推送到分支 (`git push origin feature/AmazingFeature`)
5. 开启 Pull Request

### 代码规范

- 使用 Black 进行代码格式化
- 遵循 PEP 8 编码规范
- 添加适当的注释和文档字符串
- 编写单元测试

## 📄 许可证

本项目采用 MIT 许可证 - 查看 [LICENSE](LICENSE) 文件了解详情。

## 🙏 致谢

- [pyJianYingDraft](https://github.com/your-repo/pyJianYingDraft) - 剪映草稿操作库
- [Google Gemini](https://ai.google.dev/) - AI 大模型支持
- [Rich](https://github.com/Textualize/rich) - 终端美化库
- [Click](https://github.com/pallets/click) - 命令行界面框架
- [Librosa](https://github.com/librosa/librosa) - 音频分析库

## 📞 支持

如果你遇到问题或有建议，请：

1. 查看 [常见问题](#-常见问题)
2. 搜索 [Issues](https://github.com/your-username/pyjianyin/issues)
3. 创建新的 Issue
4. 参与 [Discussions](https://github.com/your-username/pyjianyin/discussions)

## 🗺️ 路线图

### v0.2.0 (计划中)
- [ ] 支持更多视频格式
- [ ] 添加批量处理功能
- [ ] 优化 AI 分析准确性
- [ ] 添加 Web 界面

### v0.3.0 (计划中)
- [ ] 支持自定义 AI 模型
- [ ] 添加视频预览功能
- [ ] 支持多语言字幕
- [ ] 添加模板系统

### v1.0.0 (长期目标)
- [ ] 完整的 GUI 应用
- [ ] 云端素材库
- [ ] 协作功能
- [ ] 商业版本

## 📊 项目统计

- 🌟 Star 数量: ![GitHub stars](https://img.shields.io/github/stars/your-username/pyjianyin)
- 🍴 Fork 数量: ![GitHub forks](https://img.shields.io/github/forks/your-username/pyjianyin)
- 🐛 Issue 数量: ![GitHub issues](https://img.shields.io/github/issues/your-username/pyjianyin)
- 📝 代码行数: ![Lines of code](https://img.shields.io/tokei/lines/github/your-username/pyjianyin)

---

⭐ 如果这个项目对你有帮助，请给我们一个 Star！

💡 有想法或建议？欢迎提交 Issue 或 Pull Request！

🚀 让我们一起用 AI 革命视频创作！