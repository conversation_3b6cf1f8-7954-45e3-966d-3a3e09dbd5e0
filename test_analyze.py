#!/usr/bin/env python3
"""
测试素材分析功能
"""

import sys
from pathlib import Path

# 添加项目根目录到路径
sys.path.append(str(Path(__file__).parent))

from asset_manager.asset_analyzer import analyze_all_assets
from config import get_config

def main():
    config = get_config()
    print("开始分析素材...")
    
    success = analyze_all_assets(
        root_dir=str(config.assets_dir),
        output_json=str(config.assets_json),
        force_reanalyze=False,
        skip_network_check=True
    )
    
    print(f"分析结果: {'成功' if success else '失败'}")

if __name__ == "__main__":
    main()
