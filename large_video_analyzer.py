#!/usr/bin/env python3
"""
大视频分析器
对超过20MB的视频进行分割、分次解读，并合并结果
"""

import os
import sys
import json
from pathlib import Path
from typing import List, Dict, Optional
from rich.console import Console
from rich.progress import Progress, SpinnerColumn, TextColumn, BarColumn, TaskProgressColumn
from rich.panel import Panel

# 添加项目根目录到路径
sys.path.append(str(Path(__file__).parent))
from config import get_config
from video_splitter import VideoSplitter, check_ffmpeg_availability

console = Console()
config = get_config()

class LargeVideoAnalyzer:
    """大视频分析器类"""
    
    def __init__(self):
        self.gemini_client = None
        self.gemini_types = None
        self._init_gemini()
    
    def _init_gemini(self):
        """初始化 Gemini 客户端"""
        try:
            from google import genai
            from google.genai import types
            
            if not config.validate_api_key():
                raise Exception("API Key 未配置")
            
            os.environ['GOOGLE_API_KEY'] = config.gemini_api_key
            self.gemini_client = genai.Client()
            self.gemini_types = types
            
        except ImportError:
            raise Exception("新版 Google GenAI SDK 未安装")
        except Exception as e:
            raise Exception(f"Gemini 初始化失败: {e}")
    
    def analyze_video_segment(self, segment_path: str, segment_info: Dict, context: Dict = None) -> Optional[Dict]:
        """
        分析单个视频片段
        
        Args:
            segment_path: 片段文件路径
            segment_info: 片段信息
            context: 上下文信息（前面片段的分析结果）
            
        Returns:
            Dict: 分析结果
        """
        try:
            # 读取视频数据
            with open(segment_path, 'rb') as f:
                video_data = f.read()
            
            # 构建提示词，包含上下文信息
            prompt = self._build_segment_prompt(segment_info, context)
            
            # 创建多模态内容
            video_part = self.gemini_types.Part(
                inline_data=self.gemini_types.Blob(
                    data=video_data,
                    mime_type='video/mp4'
                )
            )
            
            content = self.gemini_types.Content(parts=[
                self.gemini_types.Part(text=prompt),
                video_part
            ])
            
            # 调用 API
            response = self.gemini_client.models.generate_content(
                model=f'models/{config.gemini_model_pro}',
                contents=content
            )
            
            if not response or not response.text:
                return None
            
            # 解析响应
            cleaned_text = response.text.strip()
            cleaned_text = cleaned_text.replace('```json', '').replace('```', '').strip()
            
            try:
                result = json.loads(cleaned_text)
                
                # 添加片段元信息
                result['segment_info'] = {
                    'index': segment_info['index'],
                    'start_time': segment_info['start_time'],
                    'end_time': segment_info['end_time'],
                    'duration': segment_info['duration']
                }
                
                return result
                
            except json.JSONDecodeError:
                # 如果JSON解析失败，返回文本结果
                return {
                    'segment_info': {
                        'index': segment_info['index'],
                        'start_time': segment_info['start_time'],
                        'end_time': segment_info['end_time'],
                        'duration': segment_info['duration']
                    },
                    'description': cleaned_text,
                    'tags': [],
                    'mood': 'unknown',
                    'category': 'unknown',
                    'quality': 'unknown'
                }
                
        except Exception as e:
            console.print(f"❌ [red]分析片段 {segment_info['index']} 失败: {e}[/red]")
            return None
    
    def _build_segment_prompt(self, segment_info: Dict, context: Dict = None) -> str:
        """构建片段分析提示词"""
        
        base_prompt = f"""
Analyze this video segment (part {segment_info['index'] + 1}) and provide a JSON output with the following keys:
- "description": A detailed description of the content in Chinese.
- "tags": A list of relevant keywords in English.
- "mood": The overall mood in English.
- "category": Content category.
- "quality": Visual/audio quality as "high", "medium", or "low".
- "key_moments": Important moments or transitions in this segment.
- "continuity_notes": Notes about how this segment connects to the overall video.

Segment timing: {segment_info['start_time']:.1f}s - {segment_info['end_time']:.1f}s (duration: {segment_info['duration']:.1f}s)
"""
        
        if context and context.get('previous_segments'):
            # 添加上下文信息
            prev_segments = context['previous_segments'][-2:]  # 最近2个片段的信息
            
            context_info = "\n\nContext from previous segments:\n"
            for prev in prev_segments:
                context_info += f"- Segment {prev['segment_info']['index']}: {prev.get('description', 'N/A')[:100]}...\n"
            
            base_prompt += context_info
            base_prompt += "\nPlease ensure continuity with the previous segments and note any transitions or connections."
        
        base_prompt += "\n\nPlease respond with valid JSON only."
        
        return base_prompt
    
    def merge_segment_results(self, segment_results: List[Dict]) -> Dict:
        """
        合并多个片段的分析结果
        
        Args:
            segment_results: 片段分析结果列表
            
        Returns:
            Dict: 合并后的完整分析结果
        """
        if not segment_results:
            return {}
        
        # 收集所有标签
        all_tags = []
        all_moods = []
        all_categories = []
        all_qualities = []
        
        # 合并描述
        descriptions = []
        key_moments = []
        
        for i, result in enumerate(segment_results):
            segment_info = result.get('segment_info', {})
            
            # 添加时间戳的描述
            desc = result.get('description', '')
            if desc:
                time_range = f"[{segment_info.get('start_time', 0):.1f}s-{segment_info.get('end_time', 0):.1f}s]"
                descriptions.append(f"{time_range} {desc}")
            
            # 收集标签和属性
            if result.get('tags'):
                all_tags.extend(result['tags'])
            if result.get('mood'):
                all_moods.append(result['mood'])
            if result.get('category'):
                all_categories.append(result['category'])
            if result.get('quality'):
                all_qualities.append(result['quality'])
            if result.get('key_moments'):
                key_moments.extend(result['key_moments'])
        
        # 统计最常见的属性
        def most_common(items):
            if not items:
                return 'unknown'
            return max(set(items), key=items.count)
        
        # 去重标签并保持顺序
        unique_tags = []
        for tag in all_tags:
            if tag not in unique_tags:
                unique_tags.append(tag)
        
        # 计算总时长
        total_duration = sum(r.get('segment_info', {}).get('duration', 0) for r in segment_results)
        
        # 分类时长
        duration_category = "long"
        if total_duration < 5:
            duration_category = "short"
        elif total_duration < 30:
            duration_category = "medium"
        
        merged_result = {
            'description': ' '.join(descriptions),
            'tags': unique_tags[:10],  # 限制标签数量
            'mood': most_common(all_moods),
            'category': most_common(all_categories),
            'quality': most_common(all_qualities),
            'duration_category': duration_category,
            'total_duration': total_duration,
            'segment_count': len(segment_results),
            'key_moments': key_moments,
            'analysis_method': 'segmented_analysis',
            'segments_summary': [
                {
                    'index': r.get('segment_info', {}).get('index', i),
                    'time_range': f"{r.get('segment_info', {}).get('start_time', 0):.1f}s-{r.get('segment_info', {}).get('end_time', 0):.1f}s",
                    'description': r.get('description', '')[:100] + ('...' if len(r.get('description', '')) > 100 else '')
                }
                for i, r in enumerate(segment_results)
            ]
        }
        
        return merged_result
    
    def analyze_large_video(self, video_path: str) -> Optional[Dict]:
        """
        分析大视频文件
        
        Args:
            video_path: 视频文件路径
            
        Returns:
            Dict: 完整的分析结果
        """
        console.print(f"🎬 [bold blue]开始分析大视频: {Path(video_path).name}[/bold blue]")
        
        try:
            with VideoSplitter() as splitter:
                # 分割视频
                segments = splitter.split_video(video_path)
                
                if not segments:
                    console.print("❌ [red]视频分割失败[/red]")
                    return None
                
                # 分析每个片段
                segment_results = []
                context = {'previous_segments': []}
                
                with Progress(
                    SpinnerColumn(),
                    TextColumn("[progress.description]{task.description}"),
                    BarColumn(),
                    TaskProgressColumn(),
                    console=console
                ) as progress:
                    
                    analyze_task = progress.add_task("分析视频片段...", total=len(segments))
                    
                    for segment in segments:
                        if not segment.get('within_limit', True):
                            progress.console.print(f"⚠️ [yellow]跳过超大片段 {segment['index']}[/yellow]")
                            progress.advance(analyze_task)
                            continue
                        
                        progress.console.print(f"🔍 分析片段 {segment['index']}: "
                                             f"{segment['start_time']:.1f}s-{segment['end_time']:.1f}s")
                        
                        result = self.analyze_video_segment(
                            segment['file_path'], 
                            segment, 
                            context
                        )
                        
                        if result:
                            segment_results.append(result)
                            context['previous_segments'].append(result)
                            
                            # 只保留最近的几个片段作为上下文
                            if len(context['previous_segments']) > 3:
                                context['previous_segments'] = context['previous_segments'][-3:]
                            
                            progress.console.print(f"  ✅ 片段 {segment['index']} 分析完成")
                        else:
                            progress.console.print(f"  ❌ 片段 {segment['index']} 分析失败")
                        
                        progress.advance(analyze_task)
                
                # 合并结果
                if segment_results:
                    console.print(f"🔄 [bold]合并 {len(segment_results)} 个片段的分析结果...[/bold]")
                    merged_result = self.merge_segment_results(segment_results)
                    
                    console.print(f"✅ [green]大视频分析完成！[/green]")
                    console.print(f"📊 总时长: {merged_result.get('total_duration', 0):.1f}秒")
                    console.print(f"📋 片段数: {merged_result.get('segment_count', 0)}")
                    console.print(f"🏷️ 主要标签: {', '.join(merged_result.get('tags', [])[:5])}")
                    
                    return merged_result
                else:
                    console.print("❌ [red]所有片段分析都失败了[/red]")
                    return None
                    
        except Exception as e:
            console.print(f"❌ [red]大视频分析失败: {e}[/red]")
            return None

def test_large_video_analysis():
    """测试大视频分析功能"""
    console.print(Panel(
        "[bold blue]大视频分析测试[/bold blue]\n" +
        "测试视频分割和AI分析的完整流程",
        border_style="blue"
    ))
    
    # 检查依赖
    if not check_ffmpeg_availability():
        console.print("[red]ERROR:[/red] FFmpeg 未安装")
        return False

    if not config.validate_api_key():
        console.print("[red]ERROR:[/red] API Key 未配置")
        return False
    
    # 查找测试视频
    test_videos = []

    try:
        if not config.assets_dir.exists():
            console.print(f"⚠️ [yellow]素材目录不存在: {config.assets_dir}[/yellow]")
            console.print("💡 [yellow]请运行 'python main.py setup' 创建素材目录[/yellow]")
            return True

        for video_path in config.assets_dir.rglob("*.mp4"):
            if video_path.exists():
                file_size = video_path.stat().st_size / 1024 / 1024
                if file_size > 20:
                    test_videos.append((video_path, file_size))
    except Exception as e:
        console.print(f"❌ [red]扫描视频文件时出错: {e}[/red]")
        return False

    if not test_videos:
        console.print("⚠️ [yellow]未找到大于20MB的测试视频[/yellow]")
        console.print("💡 [yellow]请添加一些大视频文件到素材目录进行测试[/yellow]")
        return True
    
    # 选择最小的大视频进行测试
    test_video, file_size = min(test_videos, key=lambda x: x[1])
    console.print(f"📹 测试视频: {test_video.name} ({file_size:.1f}MB)")
    
    try:
        analyzer = LargeVideoAnalyzer()
        result = analyzer.analyze_large_video(str(test_video))
        
        if result:
            console.print("\n📋 [bold]分析结果摘要:[/bold]")
            console.print(f"描述: {result.get('description', 'N/A')[:200]}...")
            console.print(f"标签: {', '.join(result.get('tags', []))}")
            console.print(f"情绪: {result.get('mood', 'N/A')}")
            console.print(f"类别: {result.get('category', 'N/A')}")
            console.print(f"质量: {result.get('quality', 'N/A')}")
            
            return True
        else:
            console.print("❌ [red]分析失败[/red]")
            return False
            
    except Exception as e:
        console.print(f"❌ [red]测试异常: {e}[/red]")
        return False

if __name__ == "__main__":
    test_large_video_analysis()
