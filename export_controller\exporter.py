import pyJianYingDraft as draft
import time

# 自动化导出视频
# draft_name: 剪映草稿名称
# export_path: 导出视频路径
def export_video(draft_name, export_path):
    print("请确保剪映已打开并停留在主页...")
    time.sleep(5) # 等待用户准备
    try:
        ctrl = draft.JianyingController()
        ctrl.export_draft(
            draft_name,
            export_path,
            resolution=draft.ExportResolution.RES_1080P,
            framerate=draft.ExportFramerate.FR_30
        )
        print(f"视频已成功导出至: {export_path}")
    except Exception as e:
        print(f"导出失败: {e}")
        print("请注意：批量导出功能仅支持Windows和剪映6及以下版本。")

if __name__ == "__main__":
    # 示例用法
    export_video(
        draft_name="draft_content.json", # 草稿文件名
        export_path="output.mp4"         # 导出视频路径
    ) 