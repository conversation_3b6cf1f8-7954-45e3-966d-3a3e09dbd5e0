#!/usr/bin/env python3
"""
网络诊断工具
用于检查和诊断 PyJianYin 的网络连接问题
"""
import os
import sys
import time
import requests
import socket
from pathlib import Path
from rich.console import Console
from rich.table import Table
from rich.panel import Panel
from rich.progress import Progress, SpinnerColumn, TextColumn

# 添加项目根目录到路径
sys.path.append(str(Path(__file__).parent))
from config import get_config

console = Console()
config = get_config()

def test_basic_connectivity():
    """测试基本网络连接"""
    console.print("🌐 [bold]测试基本网络连接...[/bold]")
    
    test_sites = [
        ("Google", "https://www.google.com"),
        ("百度", "https://www.baidu.com"),
        ("GitHub", "https://github.com"),
    ]
    
    results = []
    
    for name, url in test_sites:
        try:
            start_time = time.time()
            response = requests.get(url, timeout=10)
            end_time = time.time()
            
            if response.status_code == 200:
                latency = (end_time - start_time) * 1000
                results.append((name, "✅ 正常", f"{latency:.0f}ms"))
            else:
                results.append((name, f"❌ 错误 {response.status_code}", "-"))
                
        except requests.exceptions.Timeout:
            results.append((name, "⏰ 超时", "-"))
        except requests.exceptions.ConnectionError:
            results.append((name, "❌ 连接失败", "-"))
        except Exception as e:
            results.append((name, f"❌ {str(e)[:20]}", "-"))
    
    # 显示结果表格
    table = Table(title="网络连接测试")
    table.add_column("网站", style="cyan")
    table.add_column("状态", style="magenta")
    table.add_column("延迟", style="green")
    
    for name, status, latency in results:
        table.add_row(name, status, latency)
    
    console.print(table)
    
    # 返回是否有成功的连接
    return any("✅" in result[1] for result in results)

def test_dns_resolution():
    """测试 DNS 解析"""
    console.print("\n🔍 [bold]测试 DNS 解析...[/bold]")
    
    domains = [
        "google.com",
        "generativelanguage.googleapis.com",
        "ai.google.dev"
    ]
    
    results = []
    
    for domain in domains:
        try:
            start_time = time.time()
            ip = socket.gethostbyname(domain)
            end_time = time.time()
            
            latency = (end_time - start_time) * 1000
            results.append((domain, ip, f"{latency:.0f}ms"))
            
        except socket.gaierror:
            results.append((domain, "❌ 解析失败", "-"))
        except Exception as e:
            results.append((domain, f"❌ {str(e)[:20]}", "-"))
    
    # 显示结果表格
    table = Table(title="DNS 解析测试")
    table.add_column("域名", style="cyan")
    table.add_column("IP 地址", style="magenta")
    table.add_column("解析时间", style="green")
    
    for domain, ip, latency in results:
        table.add_row(domain, ip, latency)
    
    console.print(table)
    
    return all("❌" not in result[1] for result in results)

def test_gemini_api():
    """测试 Gemini API 连接"""
    console.print("\n🤖 [bold]测试 Gemini API 连接...[/bold]")
    
    if not config.validate_api_key():
        console.print("❌ [red]API Key 未配置，跳过 API 测试[/red]")
        return False
    
    try:
        import google.generativeai as genai
        
        # 配置 API
        genai.configure(api_key=config.gemini_api_key)
        
        # 测试简单的文本生成
        model = genai.GenerativeModel('gemini-pro')
        
        with Progress(
            SpinnerColumn(),
            TextColumn("[progress.description]{task.description}"),
            console=console
        ) as progress:
            task = progress.add_task("测试 API 调用...", total=None)
            
            response = model.generate_content("Hello, this is a test.")
            
            if response and response.text:
                console.print("✅ [green]Gemini API 连接正常[/green]")
                console.print(f"📝 测试响应: {response.text[:50]}...")
                return True
            else:
                console.print("❌ [red]API 响应异常[/red]")
                return False
                
    except Exception as e:
        console.print(f"❌ [red]Gemini API 测试失败: {str(e)}[/red]")
        return False

def test_file_upload():
    """测试文件上传功能"""
    console.print("\n📤 [bold]测试文件上传功能...[/bold]")
    
    if not config.validate_api_key():
        console.print("❌ [red]API Key 未配置，跳过上传测试[/red]")
        return False
    
    # 创建一个小的测试文件
    test_file = Path("test_upload.txt")
    try:
        with open(test_file, "w", encoding="utf-8") as f:
            f.write("这是一个测试文件，用于验证上传功能。")
        
        import google.generativeai as genai
        genai.configure(api_key=config.gemini_api_key)
        
        with Progress(
            SpinnerColumn(),
            TextColumn("[progress.description]{task.description}"),
            console=console
        ) as progress:
            task = progress.add_task("上传测试文件...", total=None)
            
            # 上传文件
            uploaded_file = genai.upload_file(path=str(test_file))
            
            # 等待处理完成
            while uploaded_file.state.name == "PROCESSING":
                time.sleep(1)
                uploaded_file = genai.get_file(uploaded_file.name)
            
            if uploaded_file.state.name == "ACTIVE":
                console.print("✅ [green]文件上传功能正常[/green]")
                
                # 清理上传的文件
                genai.delete_file(uploaded_file.name)
                return True
            else:
                console.print(f"❌ [red]文件上传失败: {uploaded_file.state.name}[/red]")
                return False
                
    except Exception as e:
        console.print(f"❌ [red]文件上传测试失败: {str(e)}[/red]")
        return False
    finally:
        # 清理本地测试文件
        if test_file.exists():
            test_file.unlink()

def get_system_info():
    """获取系统信息"""
    import platform
    import sys
    
    info = {
        "操作系统": platform.system(),
        "系统版本": platform.version(),
        "Python 版本": sys.version.split()[0],
        "网络代理": "已设置" if any(key in os.environ for key in ['HTTP_PROXY', 'HTTPS_PROXY', 'http_proxy', 'https_proxy']) else "未设置"
    }
    
    return info

def show_recommendations(test_results):
    """显示建议和解决方案"""
    console.print("\n💡 [bold blue]诊断建议[/bold blue]")
    
    if not test_results["basic_connectivity"]:
        console.print("🔧 [yellow]基本网络连接问题:[/yellow]")
        console.print("  • 检查网络连接是否正常")
        console.print("  • 检查防火墙设置")
        console.print("  • 尝试使用 VPN 或代理")
    
    if not test_results["dns_resolution"]:
        console.print("🔧 [yellow]DNS 解析问题:[/yellow]")
        console.print("  • 尝试更换 DNS 服务器（如 8.8.8.8）")
        console.print("  • 清除 DNS 缓存")
        console.print("  • 检查网络配置")
    
    if not test_results["gemini_api"]:
        console.print("🔧 [yellow]Gemini API 问题:[/yellow]")
        console.print("  • 检查 API Key 是否正确")
        console.print("  • 确认 API Key 有足够的配额")
        console.print("  • 检查是否在支持的地区")
    
    if not test_results["file_upload"]:
        console.print("🔧 [yellow]文件上传问题:[/yellow]")
        console.print("  • 检查文件大小（建议 < 50MB）")
        console.print("  • 确认网络连接稳定")
        console.print("  • 尝试减少并发上传数量")
    
    console.print("\n🚀 [bold green]优化建议:[/bold green]")
    console.print("  • 使用稳定的网络连接")
    console.print("  • 避免在网络高峰期进行大量分析")
    console.print("  • 分批处理大量文件")
    console.print("  • 定期清理临时文件")

def main():
    """主诊断函数"""
    console.print(Panel(
        "[bold blue]PyJianYin 网络诊断工具[/bold blue]\n" +
        "检查网络连接和 API 访问状态",
        border_style="blue"
    ))
    
    # 显示系统信息
    console.print("\n📊 [bold]系统信息[/bold]")
    import os
    system_info = get_system_info()
    for key, value in system_info.items():
        console.print(f"  • {key}: {value}")
    
    # 运行诊断测试
    test_results = {}
    
    test_results["basic_connectivity"] = test_basic_connectivity()
    test_results["dns_resolution"] = test_dns_resolution()
    test_results["gemini_api"] = test_gemini_api()
    test_results["file_upload"] = test_file_upload()
    
    # 显示总结
    console.print("\n" + "="*50)
    console.print("📋 [bold]诊断结果总结[/bold]")
    console.print("="*50)
    
    passed_tests = sum(test_results.values())
    total_tests = len(test_results)
    
    if passed_tests == total_tests:
        console.print("🎉 [bold green]所有测试通过！网络连接正常。[/bold green]")
    else:
        console.print(f"⚠️ [yellow]部分测试失败 ({passed_tests}/{total_tests})[/yellow]")
        show_recommendations(test_results)
    
    return passed_tests == total_tests

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
