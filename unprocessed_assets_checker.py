#!/usr/bin/env python3
"""
未处理素材检测和处理工具
检测素材目录中未被分析的文件，并提供处理选项
"""

import sys
import json
from pathlib import Path
from typing import List, Dict, Set
from rich.table import Table
from rich.panel import Panel
from rich.prompt import Confirm, Prompt
from rich.progress import Progress, SpinnerColumn, TextColumn

# 添加项目根目录到路径
sys.path.append(str(Path(__file__).parent))
from config import get_config
from console_utils import safe_console, print_success, print_error, print_warning, print_info
from asset_manager.asset_analyzer import analyze_media_with_retry, analyze_large_video

console = safe_console
config = get_config()

def load_processed_assets() -> Set[str]:
    """加载已处理的素材文件路径"""
    processed_paths = set()
    
    if config.assets_json.exists():
        try:
            with open(config.assets_json, 'r', encoding='utf-8') as f:
                assets_data = json.load(f)
                for asset in assets_data:
                    processed_paths.add(Path(asset['path']).resolve().as_posix())
            print_info(f"加载了 {len(processed_paths)} 个已处理文件记录")
        except Exception as e:
            print_error(f"加载已处理文件记录失败: {e}")
    else:
        print_warning("未找到 assets.json 文件，所有文件将被视为未处理")
    
    return processed_paths

def scan_all_assets() -> List[Dict]:
    """扫描素材目录中的所有支持的文件"""
    all_files = []
    
    if not config.assets_dir.exists():
        print_error(f"素材目录不存在: {config.assets_dir}")
        return all_files
    
    print_info(f"扫描素材目录: {config.assets_dir}")
    
    for file_path in config.assets_dir.rglob("*"):
        if file_path.is_file() and config.is_supported_file(str(file_path)):
            # 获取文件信息
            try:
                file_stat = file_path.stat()
                file_size = file_stat.st_size
                size_mb = file_size / 1024 / 1024
                
                # 检查文件大小
                size_ok, size_info = config.check_file_size(str(file_path))
                
                # 确定文件类型
                file_type = "image"
                if file_path.suffix.lower() in config.supported_video_formats:
                    file_type = "video"
                elif file_path.suffix.lower() in config.supported_audio_formats:
                    file_type = "audio"
                
                file_info = {
                    "path": file_path.resolve().as_posix(),
                    "name": file_path.name,
                    "type": file_type,
                    "size_bytes": file_size,
                    "size_mb": size_mb,
                    "size_formatted": f"{size_mb:.1f} MB",
                    "within_limit": size_ok,
                    "size_info": size_info,
                    "relative_path": file_path.relative_to(config.assets_dir)
                }
                
                all_files.append(file_info)
                
            except Exception as e:
                print_warning(f"无法获取文件信息: {file_path.name} - {e}")
    
    return all_files

def identify_unprocessed_files(all_files: List[Dict], processed_paths: Set[str]) -> List[Dict]:
    """识别未处理的文件"""
    unprocessed = []
    
    for file_info in all_files:
        if file_info["path"] not in processed_paths:
            unprocessed.append(file_info)
    
    return unprocessed

def display_unprocessed_summary(unprocessed_files: List[Dict]):
    """显示未处理文件的摘要"""
    if not unprocessed_files:
        print_success("🎉 所有支持的素材文件都已处理完成！")
        return
    
    console.print(f"\n📋 [bold]未处理文件摘要[/bold]")
    
    # 统计信息
    stats = {
        "total": len(unprocessed_files),
        "video": len([f for f in unprocessed_files if f["type"] == "video"]),
        "image": len([f for f in unprocessed_files if f["type"] == "image"]),
        "audio": len([f for f in unprocessed_files if f["type"] == "audio"]),
        "within_limit": len([f for f in unprocessed_files if f["within_limit"]]),
        "over_limit": len([f for f in unprocessed_files if not f["within_limit"]])
    }
    
    stats_table = Table(show_header=True, header_style="bold blue")
    stats_table.add_column("类型", style="cyan")
    stats_table.add_column("数量", style="white")
    stats_table.add_column("状态", style="green")
    
    stats_table.add_row("总文件数", str(stats["total"]), "")
    stats_table.add_row("视频文件", str(stats["video"]), "")
    stats_table.add_row("图片文件", str(stats["image"]), "")
    stats_table.add_row("音频文件", str(stats["audio"]), "")
    stats_table.add_row("符合大小限制", str(stats["within_limit"]), "OK" if stats["over_limit"] == 0 else "WARN")
    stats_table.add_row("超过大小限制", str(stats["over_limit"]), "ERROR" if stats["over_limit"] > 0 else "OK")
    
    console.print(stats_table)
    
    # 详细文件列表
    console.print(f"\n📄 [bold]未处理文件列表[/bold]")
    
    files_table = Table(show_header=True, header_style="bold blue")
    files_table.add_column("文件名", style="white")
    files_table.add_column("类型", style="cyan")
    files_table.add_column("大小", style="yellow")
    files_table.add_column("状态", style="green")
    files_table.add_column("路径", style="dim")
    
    for file_info in unprocessed_files:
        status = "✅ 可处理" if file_info["within_limit"] else "⚠️ 超大小限制"
        if not file_info["within_limit"] and file_info["type"] == "video":
            status = "🎬 可分割处理"
        
        files_table.add_row(
            file_info["name"],
            file_info["type"].upper(),
            file_info["size_formatted"],
            status,
            str(file_info["relative_path"].parent) if file_info["relative_path"].parent != Path(".") else "根目录"
        )
    
    console.print(files_table)

def process_single_file(file_info: Dict) -> bool:
    """处理单个文件"""
    file_path = file_info["path"]
    file_name = file_info["name"]
    
    console.print(f"\n🔄 [cyan]处理文件: {file_name}[/cyan]")
    
    try:
        # 检查文件大小，如果是大视频文件，使用分割分析
        if not file_info["within_limit"] and file_info["type"] == "video":
            console.print(f"📹 [yellow]检测到大视频文件，使用分割分析[/yellow]")
            result = analyze_large_video(file_path, file_info["size_info"])
        else:
            result = analyze_media_with_retry(file_path)
        
        if result:
            print_success(f"分析成功: {file_name}")
            return True
        else:
            print_error(f"分析失败: {file_name}")
            return False
            
    except Exception as e:
        print_error(f"处理文件时出错: {file_name} - {e}")
        return False

def process_unprocessed_files(unprocessed_files: List[Dict]) -> bool:
    """批量处理未处理的文件"""
    if not unprocessed_files:
        print_info("没有需要处理的文件")
        return True
    
    console.print(f"\n🚀 [bold]开始处理 {len(unprocessed_files)} 个未处理文件[/bold]")
    
    # 加载现有的 assets.json
    existing_assets = []
    if config.assets_json.exists():
        try:
            with open(config.assets_json, 'r', encoding='utf-8') as f:
                existing_assets = json.load(f)
        except Exception as e:
            print_warning(f"无法加载现有的 assets.json: {e}")
    
    success_count = 0
    failed_count = 0
    
    with Progress() as progress:
        task = progress.add_task("处理文件...", total=len(unprocessed_files))
        
        for i, file_info in enumerate(unprocessed_files):
            progress.update(task, description=f"处理: {file_info['name']}")
            
            if process_single_file(file_info):
                success_count += 1
                
                # 重新分析成功后，需要重新加载 assets.json 来获取新的分析结果
                # 这里简化处理，让用户重新运行 analyze 命令
                
            else:
                failed_count += 1
                
                # 如果连续失败太多，询问是否继续
                if failed_count >= 3 and i < len(unprocessed_files) - 1:
                    console.print(f"\n⚠️ [yellow]已有 {failed_count} 个文件处理失败[/yellow]")
                    if not Confirm.ask("是否继续处理剩余文件？"):
                        break
                    failed_count = 0  # 重置计数器
            
            progress.advance(task)
    
    # 显示处理结果
    console.print(f"\n📊 [bold]处理结果[/bold]")
    console.print(f"✅ 成功: {success_count} 个文件")
    console.print(f"❌ 失败: {failed_count} 个文件")
    
    if success_count > 0:
        print_success("部分文件处理成功！建议重新运行 'python main.py analyze' 来更新 assets.json")
    
    return failed_count == 0

def main():
    """主函数"""
    try:
        console.print(Panel(
            "[bold blue]PyJianYin 未处理素材检测工具[/bold blue]\n" +
            "检测和处理素材目录中未被分析的文件",
            border_style="blue"
        ))

        # 1. 加载已处理的文件
        console.print("\n📂 [bold]步骤 1: 加载已处理文件记录[/bold]")
        processed_paths = load_processed_assets()

        # 2. 扫描所有素材文件
        console.print("\n🔍 [bold]步骤 2: 扫描素材目录[/bold]")
        all_files = scan_all_assets()

        if not all_files:
            print_warning("未找到任何支持的素材文件")
            return

        print_info(f"找到 {len(all_files)} 个支持的素材文件")

        # 3. 识别未处理的文件
        console.print("\n🎯 [bold]步骤 3: 识别未处理文件[/bold]")
        unprocessed_files = identify_unprocessed_files(all_files, processed_paths)

        # 4. 显示摘要
        display_unprocessed_summary(unprocessed_files)

        # 5. 询问是否处理
        if unprocessed_files:
            console.print(f"\n💡 [bold]处理选项[/bold]")
            console.print("1. 🔄 立即处理所有未处理文件")
            console.print("2. 📋 仅显示列表，稍后手动处理")
            console.print("3. 🎯 选择性处理特定文件")

            choice = Prompt.ask("请选择操作", choices=["1", "2", "3"], default="2")

            if choice == "1":
                process_unprocessed_files(unprocessed_files)
            elif choice == "3":
                console.print("\n🎯 [bold]选择性处理功能开发中...[/bold]")
                console.print("💡 当前请使用 'python main.py analyze --force' 重新分析所有文件")
            else:
                console.print("\n💡 [blue]建议的下一步:[/blue]")
                console.print("  1. 运行 'python main.py analyze' 处理新文件")
                console.print("  2. 使用 'python main.py analyze --force' 重新分析所有文件")
                console.print("  3. 检查网络连接和 API 配置")

    except Exception as e:
        print_error(f"程序运行出错: {e}")
        import traceback
        console.print(f"[red]详细错误信息:[/red]")
        console.print(traceback.format_exc())

if __name__ == "__main__":
    main()
