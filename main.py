#!/usr/bin/env python3
"""
PyJianYin - 基于 Gemini AI 和 pyJianYingDraft 的自动视频生成系统
"""

import os
import sys
from pathlib import Path
import click
from rich.console import Console
from rich.panel import Panel
from rich.table import Table
from rich.prompt import Prompt, Confirm

# 添加子模块路径
project_root = Path(__file__).parent
sys.path.append(str(project_root / 'asset_manager'))
sys.path.append(str(project_root / 'storyboard_generator'))
sys.path.append(str(project_root / 'draft_builder'))
sys.path.append(str(project_root / 'export_controller'))

from config import get_config, setup_environment
from asset_analyzer import analyze_all_assets
from storyboard_generator import generate_storyboard
from draft_builder import build_draft_from_storyboard
from exporter import export_video

# 初始化
config = get_config()
console = Console()

def show_banner():
    """显示程序横幅"""
    banner = """
    ██████╗ ██╗   ██╗     ██╗██╗ █████╗ ███╗   ██╗██╗   ██╗██╗███╗   ██╗
    ██╔══██╗╚██╗ ██╔╝     ██║██║██╔══██╗████╗  ██║╚██╗ ██╔╝██║████╗  ██║
    ██████╔╝ ╚████╔╝      ██║██║███████║██╔██╗ ██║ ╚████╔╝ ██║██╔██╗ ██║
    ██╔═══╝   ╚██╔╝  ██   ██║██║██╔══██║██║╚██╗██║  ╚██╔╝  ██║██║╚██╗██║
    ██║        ██║   ╚█████╔╝██║██║  ██║██║ ╚████║   ██║   ██║██║ ╚████║
    ╚═╝        ╚═╝    ╚════╝ ╚═╝╚═╝  ╚═╝╚═╝  ╚═══╝   ╚═╝   ╚═╝╚═╝  ╚═══╝
    """

    console.print(Panel(
        banner + "\n\n[bold blue]基于 Gemini AI 和 pyJianYingDraft 的自动视频生成系统[/bold blue]\n" +
        "[dim]让 AI 成为你的视频创作助手[/dim]",
        border_style="blue"
    ))

def check_environment():
    """检查环境配置"""
    console.print("🔍 [bold]检查环境配置...[/bold]")

    issues = []

    # 检查 API Key
    if not config.validate_api_key():
        issues.append("❌ Gemini API Key 未配置")
    else:
        console.print("✅ Gemini API Key 已配置")

        # 检查 Gemini SDK
        try:
            from gemini_adapter import get_gemini_adapter
            adapter = get_gemini_adapter()
            sdk_info = adapter.get_sdk_info()
            console.print(f"🤖 SDK 类型: {sdk_info['sdk_type']}")
            console.print(f"⚡ 当前模型: {sdk_info['model_pro']}")
        except Exception as e:
            issues.append(f"❌ Gemini SDK 初始化失败: {e}")

    # 检查素材目录
    if not config.assets_dir.exists():
        issues.append(f"❌ 素材目录不存在: {config.assets_dir}")
    else:
        console.print(f"✅ 素材目录: {config.assets_dir}")

    # 检查示例文件
    if not config.get_examples_path().exists():
        issues.append(f"❌ 示例目录不存在: {config.get_examples_path()}")
    else:
        console.print(f"✅ 示例目录: {config.get_examples_path()}")

    if issues:
        console.print("\n⚠️ [yellow]发现以下问题:[/yellow]")
        for issue in issues:
            console.print(f"  {issue}")

        if not config.validate_api_key():
            console.print("\n💡 [blue]配置 API Key 的方法:[/blue]")
            console.print("  1. 复制 .env.example 为 .env")
            console.print("  2. 在 https://makersuite.google.com/app/apikey 获取 API Key")
            console.print("  3. 编辑 .env 文件，填入你的 API Key")

        return False

    console.print("\n✅ [green]环境配置正常[/green]")
    return True

@click.group()
@click.version_option(version="0.1.0")
def cli():
    """PyJianYin - AI 驱动的自动视频生成工具"""
    show_banner()

@cli.command()
def setup():
    """初始化项目环境"""
    console.print("🚀 [bold blue]初始化 PyJianYin 环境[/bold blue]")

    try:
        setup_environment()

        # 创建 .env 文件
        env_file = config.project_root / ".env"
        if not env_file.exists():
            env_example = config.project_root / ".env.example"
            if env_example.exists():
                import shutil
                shutil.copy(env_example, env_file)
                console.print(f"📝 创建配置文件: {env_file}")
                console.print("💡 请编辑 .env 文件，填入你的 Gemini API Key")

        console.print("✅ [green]环境初始化完成！[/green]")
        console.print("\n📋 [bold]下一步:[/bold]")
        console.print("  1. 编辑 .env 文件，配置 API Key")
        console.print("  2. 运行 'python main.py install-sdk' 安装新版 SDK")
        console.print("  3. 在 my_video_assets 目录下添加素材")
        console.print("  4. 运行 'python main.py analyze' 分析素材")

    except Exception as e:
        console.print(f"❌ [red]初始化失败: {e}[/red]")
        sys.exit(1)

@cli.command()
def install_sdk():
    """安装新版 Google GenAI SDK"""
    console.print("📥 [bold blue]安装新版 Google GenAI SDK[/bold blue]")

    try:
        import subprocess
        result = subprocess.run([sys.executable, "install_new_sdk.py"],
                              capture_output=False,
                              cwd=config.project_root)

        if result.returncode == 0:
            console.print("\n✅ [green]SDK 安装助手运行完成[/green]")
        else:
            console.print("\n⚠️ [yellow]SDK 安装过程中发现问题[/yellow]")

    except Exception as e:
        console.print(f"❌ [red]SDK 安装助手运行失败: {e}[/red]")

@cli.command()
def check_sizes():
    """检查素材文件大小是否符合 API 限制"""
    console.print("📏 [bold blue]检查文件大小限制[/bold blue]")

    try:
        import subprocess
        result = subprocess.run([sys.executable, "check_file_sizes.py"],
                              capture_output=False,
                              cwd=config.project_root)

        if result.returncode == 0:
            console.print("\n✅ [green]文件大小检查完成[/green]")
        else:
            console.print("\n⚠️ [yellow]文件大小检查发现问题[/yellow]")

    except Exception as e:
        console.print(f"❌ [red]文件大小检查失败: {e}[/red]")

@cli.command()
def test_video_split():
    """测试视频分割功能"""
    console.print("🎬 [bold blue]测试视频分割功能[/bold blue]")

    try:
        import subprocess
        result = subprocess.run([sys.executable, "video_splitter.py"],
                              capture_output=False,
                              cwd=config.project_root)

        if result.returncode == 0:
            console.print("\n✅ [green]视频分割测试完成[/green]")
        else:
            console.print("\n⚠️ [yellow]视频分割测试发现问题[/yellow]")

    except Exception as e:
        console.print(f"❌ [red]视频分割测试失败: {e}[/red]")

@cli.command()
def test_large_video():
    """测试大视频分析功能"""
    console.print("🎥 [bold blue]测试大视频分析功能[/bold blue]")

    try:
        import subprocess
        result = subprocess.run([sys.executable, "large_video_analyzer.py"],
                              capture_output=False,
                              cwd=config.project_root)

        if result.returncode == 0:
            console.print("\n✅ [green]大视频分析测试完成[/green]")
        else:
            console.print("\n⚠️ [yellow]大视频分析测试发现问题[/yellow]")

    except Exception as e:
        console.print(f"❌ [red]大视频分析测试失败: {e}[/red]")

@cli.command()
def test_basic():
    """测试基本功能"""
    console.print("🧪 [bold blue]测试基本功能[/bold blue]")

    try:
        import subprocess
        result = subprocess.run([sys.executable, "test_basic_functions.py"],
                              capture_output=False,
                              cwd=config.project_root)

        if result.returncode == 0:
            console.print("\n✅ [green]基本功能测试通过[/green]")
            console.print("🎉 [green]PyJianYin 核心功能正常！[/green]")
        else:
            console.print("\n⚠️ [yellow]基本功能测试发现问题[/yellow]")
            console.print("🔧 [yellow]请查看测试输出并解决相关问题[/yellow]")

    except Exception as e:
        console.print(f"❌ [red]基本功能测试失败: {e}[/red]")

@cli.command()
def fix_unicode():
    """修复 Unicode 字符兼容性问题"""
    console.print("[bold blue]修复 Unicode 字符兼容性问题[/bold blue]")

    try:
        import subprocess
        result = subprocess.run([sys.executable, "fix_unicode.py"],
                              capture_output=False,
                              cwd=config.project_root)

        if result.returncode == 0:
            console.print("\n[green]SUCCESS:[/green] Unicode 字符修复完成")
            console.print("[green]现在可以正常运行测试了[/green]")
        else:
            console.print("\n[yellow]WARNING:[/yellow] Unicode 字符修复发现问题")

    except Exception as e:
        console.print(f"[red]ERROR:[/red] Unicode 字符修复失败: {e}")

@cli.command()
def test_safe():
    """测试 Unicode 安全功能"""
    console.print("[bold blue]测试 Unicode 安全功能[/bold blue]")

    try:
        import subprocess
        result = subprocess.run([sys.executable, "test_unicode_safe.py"],
                              capture_output=False,
                              cwd=config.project_root)

        if result.returncode == 0:
            console.print("\n[green]SUCCESS:[/green] Unicode 安全测试通过")
            console.print("[green]PyJianYin 功能正常，可以开始使用！[/green]")
        else:
            console.print("\n[yellow]WARNING:[/yellow] Unicode 安全测试发现问题")
            console.print("[yellow]请查看测试输出并解决相关问题[/yellow]")

    except Exception as e:
        console.print(f"[red]ERROR:[/red] Unicode 安全测试失败: {e}")

@cli.command()
def test_workflow():
    """测试完整工作流程"""
    console.print("🔄 [bold blue]测试完整工作流程[/bold blue]")

    try:
        import subprocess
        result = subprocess.run([sys.executable, "test_complete_workflow.py"],
                              capture_output=False,
                              cwd=config.project_root)

        if result.returncode == 0:
            console.print("\n✅ [green]完整工作流测试通过[/green]")
            console.print("🎉 [green]PyJianYin 大视频分析功能完全可用！[/green]")
        else:
            console.print("\n⚠️ [yellow]完整工作流测试发现问题[/yellow]")
            console.print("🔧 [yellow]请查看测试输出并解决相关问题[/yellow]")

    except Exception as e:
        console.print(f"❌ [red]完整工作流测试失败: {e}[/red]")

@cli.command()
@click.option('--force', '-f', is_flag=True, help='强制重新分析所有素材')
@click.option('--skip-network-check', is_flag=True, help='跳过网络连接检查')
@click.option('--batch-size', type=int, default=10, help='批处理大小（默认10个文件）')
def analyze(force, skip_network_check, batch_size):
    """分析素材库，生成素材数据库"""
    if not check_environment():
        return

    console.print("📊 [bold blue]分析素材库[/bold blue]")

    if skip_network_check:
        console.print("⚠️ [yellow]跳过网络检查，直接开始分析[/yellow]")

    success = analyze_all_assets(
        root_dir=str(config.assets_dir),
        output_json=str(config.assets_json),
        force_reanalyze=force,
        skip_network_check=skip_network_check
    )

    if success:
        console.print("🎉 [green]素材分析完成！[/green]")
        console.print("\n💡 [blue]提示:[/blue]")
        console.print("  • 如果有文件分析失败，可以稍后重新运行")
        console.print("  • 使用 --force 可以重新分析所有文件")
        console.print("  • 检查网络连接可以提高成功率")
    else:
        console.print("💥 [red]素材分析失败[/red]")
        console.print("\n🔧 [yellow]故障排除建议:[/yellow]")
        console.print("  1. 检查网络连接是否稳定")
        console.print("  2. 确认 Gemini API Key 配置正确")
        console.print("  3. 减少文件大小（建议 < 50MB）")
        console.print("  4. 使用 --skip-network-check 跳过网络检查")
        sys.exit(1)

@cli.command()
@click.option('--theme', '-t', prompt='视频主题', help='视频主题')
@click.option('--mode', '-m',
              type=click.Choice(['知识科普', '经济解读', '搞笑卡点', '主流视频二创']),
              prompt='视频模式',
              help='视频模式')
@click.option('--srt', '-s', help='字幕文件路径')
@click.option('--music', help='音乐文件路径')
@click.option('--output', '-o', help='输出剧本文件路径')
def storyboard(theme, mode, srt, music, output):
    """生成视频剧本"""
    if not check_environment():
        return

    # 使用默认路径
    if not srt:
        srt = str(config.default_srt_path)
    if not music:
        music = str(config.default_music_path)
    if not output:
        output = str(config.storyboard_dir / "storyboard.json")

    console.print(f"🎬 [bold blue]生成剧本: {theme}[/bold blue]")

    success = generate_storyboard(
        theme=theme,
        mode=mode,
        assets_json=str(config.assets_json),
        srt_path=srt,
        music_path=music,
        output_json=output
    )

    if success:
        console.print("🎉 [green]剧本生成完成！[/green]")
    else:
        console.print("💥 [red]剧本生成失败[/red]")
        sys.exit(1)

@cli.command()
@click.option('--storyboard', '-s', help='剧本文件路径')
@click.option('--music', help='音乐文件路径')
@click.option('--srt', help='字幕文件路径')
@click.option('--output', '-o', help='输出草稿文件路径')
@click.option('--mode', '-m',
              type=click.Choice(['知识科普', '经济解读', '搞笑卡点', '主流视频二创']),
              default='知识科普',
              help='视频模式')
def build(storyboard, music, srt, output, mode):
    """构建剪映草稿"""
    if not check_environment():
        return

    # 使用默认路径
    if not storyboard:
        storyboard = str(config.storyboard_dir / "storyboard.json")
    if not music:
        music = str(config.default_music_path)
    if not srt:
        srt = str(config.default_srt_path)
    if not output:
        output = str(config.project_root / "draft_content.json")

    console.print("🎬 [bold blue]构建剪映草稿[/bold blue]")

    success = build_draft_from_storyboard(
        storyboard_json_path=storyboard,
        assets_json_path=str(config.assets_json),
        music_path=music,
        srt_path=srt,
        output_draft_path=output,
        mode=mode
    )

    if success:
        console.print("🎉 [green]草稿构建完成！[/green]")
    else:
        console.print("💥 [red]草稿构建失败[/red]")
        sys.exit(1)

@cli.command()
@click.option('--draft', '-d', help='草稿文件路径')
@click.option('--output', '-o', help='输出视频文件路径')
def export(draft, output):
    """导出视频（需要剪映软件）"""
    if not check_environment():
        return

    # 使用默认路径
    if not draft:
        draft = str(config.project_root / "draft_content.json")
    if not output:
        output = str(config.get_output_path() / "output.mp4")

    console.print("📤 [bold blue]导出视频[/bold blue]")
    console.print("⚠️ [yellow]请确保剪映软件已打开并停留在主页[/yellow]")

    if not Confirm.ask("是否继续导出？"):
        console.print("❌ 用户取消导出")
        return

    try:
        export_video(draft_name=draft, export_path=output)
        console.print("🎉 [green]视频导出完成！[/green]")
    except Exception as e:
        console.print(f"💥 [red]视频导出失败: {e}[/red]")
        sys.exit(1)

@cli.command()
@click.option('--theme', '-t', help='视频主题')
@click.option('--mode', '-m',
              type=click.Choice(['知识科普', '经济解读', '搞笑卡点', '主流视频二创']),
              help='视频模式')
@click.option('--srt', '-s', help='字幕文件路径')
@click.option('--music', help='音乐文件路径')
@click.option('--skip-analyze', is_flag=True, help='跳过素材分析步骤')
@click.option('--skip-export', is_flag=True, help='跳过视频导出步骤')
def auto(theme, mode, srt, music, skip_analyze, skip_export):
    """一键自动生成视频（完整流程）"""
    if not check_environment():
        return

    console.print("🚀 [bold blue]一键自动视频生成[/bold blue]")

    # 交互式获取参数
    if not theme:
        theme = Prompt.ask("请输入视频主题", default="人工智能对未来工作的影响")

    if not mode:
        mode_choices = ['知识科普', '经济解读', '搞笑卡点', '主流视频二创']
        console.print("可选的视频模式:")
        for i, choice in enumerate(mode_choices, 1):
            console.print(f"  {i}. {choice}")

        mode_index = Prompt.ask("请选择视频模式", choices=['1', '2', '3', '4'], default='1')
        mode = mode_choices[int(mode_index) - 1]

    if not srt:
        srt = str(config.default_srt_path)
    if not music:
        music = str(config.default_music_path)

    # 显示配置信息
    table = Table(title="生成配置")
    table.add_column("项目", style="cyan")
    table.add_column("值", style="magenta")

    table.add_row("视频主题", theme)
    table.add_row("视频模式", mode)
    table.add_row("字幕文件", srt)
    table.add_row("音乐文件", music)
    table.add_row("跳过分析", "是" if skip_analyze else "否")
    table.add_row("跳过导出", "是" if skip_export else "否")

    console.print(table)

    if not Confirm.ask("\n是否开始生成？"):
        console.print("❌ 用户取消生成")
        return

    try:
        # 步骤1: 分析素材
        if not skip_analyze:
            console.print("\n" + "="*50)
            console.print("📊 [bold]步骤 1/4: 分析素材[/bold]")
            console.print("="*50)

            success = analyze_all_assets(
                root_dir=str(config.assets_dir),
                output_json=str(config.assets_json),
                force_reanalyze=False
            )

            if not success:
                console.print("💥 [red]素材分析失败[/red]")
                return

        # 步骤2: 生成剧本
        console.print("\n" + "="*50)
        console.print("🎬 [bold]步骤 2/4: 生成剧本[/bold]")
        console.print("="*50)

        storyboard_path = str(config.storyboard_dir / "storyboard.json")
        success = generate_storyboard(
            theme=theme,
            mode=mode,
            assets_json=str(config.assets_json),
            srt_path=srt,
            music_path=music,
            output_json=storyboard_path
        )

        if not success:
            console.print("💥 [red]剧本生成失败[/red]")
            return

        # 步骤3: 构建草稿
        console.print("\n" + "="*50)
        console.print("🎬 [bold]步骤 3/4: 构建草稿[/bold]")
        console.print("="*50)

        draft_path = str(config.project_root / "draft_content.json")
        success = build_draft_from_storyboard(
            storyboard_json_path=storyboard_path,
            assets_json_path=str(config.assets_json),
            music_path=music,
            srt_path=srt,
            output_draft_path=draft_path,
            mode=mode
        )

        if not success:
            console.print("💥 [red]草稿构建失败[/red]")
            return

        # 步骤4: 导出视频
        if not skip_export:
            console.print("\n" + "="*50)
            console.print("📤 [bold]步骤 4/4: 导出视频[/bold]")
            console.print("="*50)

            console.print("⚠️ [yellow]请确保剪映软件已打开并停留在主页[/yellow]")

            if Confirm.ask("是否继续导出视频？"):
                output_path = str(config.get_output_path() / f"{theme.replace(' ', '_')}.mp4")
                export_video(draft_name=draft_path, export_path=output_path)

        console.print("\n" + "="*50)
        console.print("🎉 [bold green]视频生成完成！[/bold green]")
        console.print("="*50)

        console.print(f"📁 草稿文件: {draft_path}")
        if not skip_export:
            console.print(f"🎬 视频文件: {output_path}")

    except Exception as e:
        console.print(f"💥 [red]自动生成失败: {e}[/red]")
        sys.exit(1)

@cli.command()
def status():
    """显示项目状态"""
    console.print("📊 [bold blue]PyJianYin 项目状态[/bold blue]")

    # 环境状态
    env_status = "✅ 正常" if check_environment() else "❌ 异常"

    # 素材统计
    assets_count = 0
    if config.assets_json.exists():
        try:
            import json
            with open(config.assets_json, 'r', encoding='utf-8') as f:
                assets = json.load(f)
                assets_count = len(assets)
        except:
            pass

    # 文件状态
    files_status = []
    files_to_check = [
        ("素材数据库", config.assets_json),
        ("示例字幕", config.default_srt_path),
        ("示例音乐", config.default_music_path),
        ("剧本文件", config.storyboard_dir / "storyboard.json"),
        ("草稿文件", config.project_root / "draft_content.json"),
    ]

    for name, path in files_to_check:
        status = "✅ 存在" if path.exists() else "❌ 不存在"
        files_status.append((name, str(path), status))

    # 创建状态表格
    table = Table(title="项目状态")
    table.add_column("项目", style="cyan")
    table.add_column("状态/路径", style="magenta")
    table.add_column("状态", style="green")

    table.add_row("环境配置", "", env_status)
    table.add_row("素材数量", str(assets_count), "")
    table.add_row("素材目录", str(config.assets_dir), "✅ 存在" if config.assets_dir.exists() else "❌ 不存在")

    for name, path, status in files_status:
        table.add_row(name, path, status)

    console.print(table)

@cli.command()
def diagnose():
    """网络连接诊断"""
    console.print("🔍 [bold blue]网络连接诊断[/bold blue]")

    try:
        import subprocess
        result = subprocess.run([sys.executable, "network_diagnostic.py"],
                              capture_output=False,
                              cwd=config.project_root)

        if result.returncode == 0:
            console.print("\n✅ [green]网络诊断完成[/green]")
        else:
            console.print("\n⚠️ [yellow]网络诊断发现问题[/yellow]")

    except Exception as e:
        console.print(f"❌ [red]诊断工具运行失败: {e}[/red]")

@cli.command()
def offline():
    """离线模式（网络不稳定时使用）"""
    console.print("🔄 [bold blue]启动离线模式[/bold blue]")

    try:
        import subprocess
        result = subprocess.run([sys.executable, "offline_mode.py"],
                              capture_output=False,
                              cwd=config.project_root)

        if result.returncode == 0:
            console.print("\n✅ [green]离线模式设置完成[/green]")
            console.print("\n💡 [blue]提示:[/blue]")
            console.print("  • 离线模式提供基础的素材管理功能")
            console.print("  • 网络恢复后建议重新运行 AI 分析")
            console.print("  • 使用 'python main.py build' 构建草稿")
        else:
            console.print("\n❌ [red]离线模式设置失败[/red]")

    except Exception as e:
        console.print(f"❌ [red]离线模式运行失败: {e}[/red]")

@cli.command()
def fix():
    """快速修复常见问题"""
    console.print("🔧 [bold blue]快速修复工具[/bold blue]")

    try:
        import subprocess
        result = subprocess.run([sys.executable, "quick_fix.py"],
                              capture_output=False,
                              cwd=config.project_root)

        if result.returncode == 0:
            console.print("\n✅ [green]修复工具运行完成[/green]")
        else:
            console.print("\n⚠️ [yellow]修复过程中发现问题[/yellow]")

    except Exception as e:
        console.print(f"❌ [red]修复工具运行失败: {e}[/red]")

@cli.command()
def test_api():
    """测试 Gemini API 功能"""
    console.print("🧪 [bold blue]测试 Gemini API 功能[/bold blue]")

    if not check_environment():
        return

    try:
        import subprocess
        result = subprocess.run([sys.executable, "test_gemini_api.py"],
                              capture_output=False,
                              cwd=config.project_root)

        if result.returncode == 0:
            console.print("\n🎉 [green]Gemini API 测试完成！[/green]")
            console.print("\n💡 [blue]提示:[/blue]")
            console.print("  • 如果所有测试通过，可以正常使用 PyJianYin")
            console.print("  • 如果测试失败，请检查网络连接和 API 配置")
            console.print("  • 运行 'python main.py diagnose' 进行详细诊断")
        else:
            console.print("\n⚠️ [yellow]API 测试发现问题[/yellow]")
            console.print("🔧 [yellow]建议运行 'python main.py fix' 进行修复[/yellow]")

    except Exception as e:
        console.print(f"❌ [red]API 测试运行失败: {e}[/red]")

@cli.command()
def test_new_api():
    """测试新版 Google GenAI SDK"""
    console.print("🧪 [bold blue]测试新版 Google GenAI SDK[/bold blue]")

    if not check_environment():
        return

    try:
        import subprocess
        result = subprocess.run([sys.executable, "test_new_api.py"],
                              capture_output=False,
                              cwd=config.project_root)

        if result.returncode == 0:
            console.print("\n🎉 [green]新版 API 测试完成！[/green]")
            console.print("\n💡 [blue]提示:[/blue]")
            console.print("  • 新版 SDK 使用了最新的 Gemini 2.5 模型")
            console.print("  • 性能和准确性都有显著提升")
            console.print("  • 建议使用新版 SDK 进行素材分析")
        else:
            console.print("\n⚠️ [yellow]新版 API 测试发现问题[/yellow]")
            console.print("🔧 [yellow]可能需要安装新版 SDK: pip install google-genai[/yellow]")

    except Exception as e:
        console.print(f"❌ [red]新版 API 测试运行失败: {e}[/red]")

if __name__ == "__main__":
    cli()
