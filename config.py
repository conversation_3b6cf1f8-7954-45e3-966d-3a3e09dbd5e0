"""
配置管理模块
管理 API 密钥、文件路径和其他配置项
"""

import os
from pathlib import Path
from typing import Optional
from dotenv import load_dotenv

# 加载环境变量
load_dotenv()

class Config:
    """配置管理类"""
    
    def __init__(self):
        self.project_root = Path(__file__).parent
        self._load_config()
    
    def _load_config(self):
        """加载配置"""
        # API 配置
        self.gemini_api_key = os.getenv("GEMINI_API_KEY", "YOUR_API_KEY")
        
        # 模型配置 (使用最新的 Gemini 2.5 模型)
        self.gemini_model_pro = "gemini-2.5-pro"
        self.gemini_model_flash = "gemini-2.5-flash"
        
        # 路径配置
        self.assets_dir = self.project_root / "my_video_assets"
        self.assets_json = self.project_root / "asset_manager" / "assets.json"
        self.storyboard_dir = self.project_root / "storyboard_generator"
        self.draft_dir = self.project_root / "draft_builder"
        self.export_dir = self.project_root / "export_controller"
        
        # 默认文件路径
        self.default_srt_path = self.project_root / "examples" / "example.srt"
        self.default_music_path = self.assets_dir / "music" / "牵丝戏-1.mp3"
        self.default_output_path = self.project_root / "output"
        
        # 支持的文件格式
        self.supported_video_formats = {".mp4", ".mov", ".avi", ".mkv", ".wmv"}
        self.supported_image_formats = {".jpg", ".jpeg", ".png", ".bmp", ".gif", ".webp"}
        self.supported_audio_formats = {".mp3", ".wav", ".aac", ".m4a", ".flac"}

        # 文件大小限制（新 API 限制 20MB）
        self.max_file_size = 20 * 1024 * 1024  # 20MB
        
        # 视频模式配置
        self.video_modes = {
            "知识科普": {
                "description": "教育性内容，节奏平稳，重视信息传达",
                "preferred_tags": ["knowledge", "education", "technology", "science"],
                "transitions": ["叠化", "闪白", "淡入淡出"],
                "pace": "moderate"
            },
            "经济解读": {
                "description": "经济分析内容，严肃专业",
                "preferred_tags": ["economics", "business", "finance", "data"],
                "transitions": ["叠化", "推拉", "擦除"],
                "pace": "moderate"
            },
            "搞笑卡点": {
                "description": "娱乐内容，快节奏，配合音乐节拍",
                "preferred_tags": ["funny", "meme", "entertainment", "dynamic"],
                "transitions": ["信号故障", "抖动", "闪烁", "缩放"],
                "pace": "fast"
            },
            "主流视频二创": {
                "description": "基于现有视频的二次创作",
                "preferred_tags": ["highlight", "key_moment", "dramatic"],
                "transitions": ["叠化", "推拉", "旋转"],
                "pace": "variable"
            }
        }
        
        # 剪映导出配置
        self.export_settings = {
            "resolution": "1080p",
            "framerate": 30,
            "quality": "high",
            "format": "mp4"
        }
    
    def validate_api_key(self) -> bool:
        """验证 API 密钥是否有效"""
        return self.gemini_api_key and self.gemini_api_key != "YOUR_API_KEY"
    
    def get_assets_path(self) -> Path:
        """获取素材目录路径"""
        self.assets_dir.mkdir(exist_ok=True)
        return self.assets_dir
    
    def get_output_path(self) -> Path:
        """获取输出目录路径"""
        self.default_output_path.mkdir(exist_ok=True)
        return self.default_output_path
    
    def get_examples_path(self) -> Path:
        """获取示例文件目录路径"""
        examples_path = self.project_root / "examples"
        examples_path.mkdir(exist_ok=True)
        return examples_path

    def get_default_music_file(self) -> Optional[Path]:
        """获取默认音乐文件路径"""
        music_dir = self.assets_dir / "music"
        if not music_dir.exists():
            return None

        # 查找第一个音频文件
        for ext in self.supported_audio_formats:
            for music_file in music_dir.glob(f"*{ext}"):
                return music_file
        return None

    def get_or_create_srt_for_music(self, music_path: Path) -> Path:
        """为音乐文件获取或创建对应的 SRT 字幕文件"""
        srt_path = music_path.with_suffix('.srt')

        if not srt_path.exists():
            # 创建一个基本的 SRT 文件
            self._create_basic_srt(srt_path, music_path)

        return srt_path

    def _create_basic_srt(self, srt_path: Path, music_path: Path):
        """创建基本的 SRT 字幕文件"""
        try:
            # 获取音频时长（简化版本，使用固定时长）
            duration = 60  # 默认60秒，实际应该通过音频分析获取

            # 生成基本字幕内容
            srt_content = f"""1
00:00:00,000 --> 00:00:05,000
欢迎观看本期视频

2
00:00:05,000 --> 00:00:10,000
今天我们来聊聊人工智能的发展历程

3
00:00:10,000 --> 00:00:15,000
从早期的概念到现在的应用

4
00:00:15,000 --> 00:00:20,000
AI技术经历了怎样的变化

5
00:00:20,000 --> 00:00:25,000
让我们一起探索这个话题

6
00:00:25,000 --> 00:00:30,000
感谢大家的观看
"""

            with open(srt_path, 'w', encoding='utf-8') as f:
                f.write(srt_content)

            print(f"✅ 创建字幕文件: {srt_path}")

        except Exception as e:
            print(f"❌ 创建字幕文件失败: {e}")
    
    def get_video_mode_config(self, mode: str) -> dict:
        """获取视频模式配置"""
        return self.video_modes.get(mode, self.video_modes["知识科普"])
    
    def is_supported_file(self, file_path: str, file_type: str = "auto") -> bool:
        """检查文件格式是否支持"""
        file_ext = Path(file_path).suffix.lower()

        if file_type == "video":
            return file_ext in self.supported_video_formats
        elif file_type == "image":
            return file_ext in self.supported_image_formats
        elif file_type == "audio":
            return file_ext in self.supported_audio_formats
        else:  # auto detect
            return (file_ext in self.supported_video_formats or
                   file_ext in self.supported_image_formats or
                   file_ext in self.supported_audio_formats)

    def check_file_size(self, file_path: str) -> tuple[bool, str]:
        """
        检查文件大小是否符合 API 限制

        Returns:
            tuple: (是否符合限制, 描述信息)
        """
        try:
            file_size = Path(file_path).stat().st_size
            size_mb = file_size / 1024 / 1024

            if file_size <= self.max_file_size:
                return True, f"{size_mb:.1f}MB"
            else:
                return False, f"{size_mb:.1f}MB (超过 20MB 限制)"
        except Exception as e:
            return False, f"无法获取文件大小: {e}"

# 全局配置实例
config = Config()

def get_config() -> Config:
    """获取配置实例"""
    return config

def setup_environment():
    """设置环境和创建必要的目录"""
    config.get_assets_path()
    config.get_output_path()
    config.get_examples_path()
    
    # 创建素材子目录
    (config.assets_dir / "videos").mkdir(exist_ok=True)
    (config.assets_dir / "images").mkdir(exist_ok=True)
    (config.assets_dir / "music").mkdir(exist_ok=True)
    (config.assets_dir / "sound").mkdir(exist_ok=True)
    
    print("✅ 环境设置完成")
    print(f"📁 素材目录: {config.assets_dir}")
    print(f"📁 输出目录: {config.default_output_path}")
    print(f"📁 示例目录: {config.get_examples_path()}")

if __name__ == "__main__":
    setup_environment()
    print(f"🔑 API Key 状态: {'✅ 已配置' if config.validate_api_key() else '❌ 未配置'}")
