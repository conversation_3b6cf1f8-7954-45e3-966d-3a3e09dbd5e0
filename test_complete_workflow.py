#!/usr/bin/env python3
"""
完整工作流测试脚本
测试从文件检查到大视频分析的完整流程
"""

import sys
import subprocess
from pathlib import Path
from rich.panel import Panel
from rich.progress import Progress, SpinnerColumn, TextColumn

# 添加项目根目录到路径
sys.path.append(str(Path(__file__).parent))
from config import get_config
from video_splitter import check_ffmpeg_availability, estimate_split_count
from console_utils import safe_console, print_success, print_error, print_warning, print_info, print_test

console = safe_console
config = get_config()

def test_dependencies():
    """测试依赖项"""
    console.print("[bold]检查依赖项...[/bold]")

    issues = []

    # 检查 API Key
    if not config.validate_api_key():
        issues.append("Gemini API Key 未配置")
    else:
        print_success("Gemini API Key 已配置")

    # 检查 FFmpeg
    if not check_ffmpeg_availability():
        issues.append("FFmpeg 未安装")
    else:
        print_success("FFmpeg 可用")

    # 检查新版 SDK
    try:
        from google import genai
        from google.genai import types
        print_success("Google GenAI SDK 可用")
    except ImportError:
        issues.append("Google GenAI SDK 未安装")

    return issues

def scan_large_videos():
    """扫描大视频文件"""
    console.print("\n📹 [bold]扫描大视频文件...[/bold]")
    
    large_videos = []
    
    if not config.assets_dir.exists():
        console.print(f"⚠️ [yellow]素材目录不存在: {config.assets_dir}[/yellow]")
        return large_videos
    
    for video_path in config.assets_dir.rglob("*.mp4"):
        if video_path.exists():
            file_size = video_path.stat().st_size / 1024 / 1024
            if file_size > 20:
                needs_split, segment_count, description = estimate_split_count(str(video_path))
                large_videos.append({
                    'path': video_path,
                    'size_mb': file_size,
                    'needs_split': needs_split,
                    'segment_count': segment_count,
                    'description': description
                })
    
    if large_videos:
        console.print(f"📊 发现 {len(large_videos)} 个大视频文件:")
        for video in large_videos:
            console.print(f"  • {video['path'].name}: {video['description']}")
    else:
        console.print("📭 未发现大于 20MB 的视频文件")
    
    return large_videos

def test_file_size_check():
    """测试文件大小检查"""
    console.print("\n📏 [bold]测试文件大小检查...[/bold]")
    
    try:
        result = subprocess.run(
            [sys.executable, "check_file_sizes.py"],
            capture_output=True,
            text=True,
            timeout=60,
            cwd=config.project_root
        )
        
        if result.returncode == 0:
            console.print("✅ 文件大小检查成功")
            return True
        else:
            console.print("❌ 文件大小检查失败")
            if result.stderr:
                console.print(f"错误: {result.stderr[:200]}...")
            return False
            
    except subprocess.TimeoutExpired:
        console.print("❌ 文件大小检查超时")
        return False
    except Exception as e:
        console.print(f"❌ 文件大小检查异常: {e}")
        return False

def test_video_splitting():
    """测试视频分割功能"""
    console.print("\n🎬 [bold]测试视频分割功能...[/bold]")
    
    try:
        result = subprocess.run(
            [sys.executable, "video_splitter.py"],
            capture_output=True,
            text=True,
            timeout=120,
            cwd=config.project_root
        )
        
        if result.returncode == 0:
            console.print("✅ 视频分割测试成功")
            return True
        else:
            console.print("❌ 视频分割测试失败")
            if result.stderr:
                console.print(f"错误: {result.stderr[:200]}...")
            return False
            
    except subprocess.TimeoutExpired:
        console.print("❌ 视频分割测试超时")
        return False
    except Exception as e:
        console.print(f"❌ 视频分割测试异常: {e}")
        return False

def test_large_video_analysis():
    """测试大视频分析功能"""
    console.print("\n🎥 [bold]测试大视频分析功能...[/bold]")
    
    try:
        result = subprocess.run(
            [sys.executable, "large_video_analyzer.py"],
            capture_output=True,
            text=True,
            timeout=300,  # 5分钟超时
            cwd=config.project_root
        )
        
        if result.returncode == 0:
            console.print("✅ 大视频分析测试成功")
            return True
        else:
            console.print("❌ 大视频分析测试失败")
            if result.stderr:
                console.print(f"错误: {result.stderr[:200]}...")
            return False
            
    except subprocess.TimeoutExpired:
        console.print("❌ 大视频分析测试超时")
        return False
    except Exception as e:
        console.print(f"❌ 大视频分析测试异常: {e}")
        return False

def test_new_api():
    """测试新版 API"""
    console.print("\n🧪 [bold]测试新版 API...[/bold]")
    
    try:
        result = subprocess.run(
            [sys.executable, "test_new_api.py"],
            capture_output=True,
            text=True,
            timeout=180,
            cwd=config.project_root
        )
        
        if result.returncode == 0:
            console.print("✅ 新版 API 测试成功")
            return True
        else:
            console.print("❌ 新版 API 测试失败")
            if result.stderr:
                console.print(f"错误: {result.stderr[:200]}...")
            return False
            
    except subprocess.TimeoutExpired:
        console.print("❌ 新版 API 测试超时")
        return False
    except Exception as e:
        console.print(f"❌ 新版 API 测试异常: {e}")
        return False

def main():
    """主测试函数"""
    console.print(Panel(
        "[bold blue]PyJianYin 完整工作流测试[/bold blue]\n" +
        "测试大视频分析的完整功能链",
        border_style="blue"
    ))
    
    # 测试步骤
    tests = [
        ("依赖检查", test_dependencies),
        ("文件大小检查", test_file_size_check),
        ("新版 API 测试", test_new_api),
        ("视频分割测试", test_video_splitting),
        ("大视频分析测试", test_large_video_analysis)
    ]
    
    results = {}
    dependency_issues = []
    
    with Progress(
        SpinnerColumn(),
        TextColumn("[progress.description]{task.description}"),
        console=console
    ) as progress:
        
        for test_name, test_func in tests:
            task = progress.add_task(f"执行 {test_name}...", total=None)
            
            console.print(f"\n{'='*60}")
            console.print(f"🧪 [bold]开始测试: {test_name}[/bold]")
            console.print('='*60)
            
            try:
                if test_name == "依赖检查":
                    dependency_issues = test_func()
                    results[test_name] = len(dependency_issues) == 0
                else:
                    results[test_name] = test_func()
            except Exception as e:
                console.print(f"❌ [red]{test_name} 测试异常: {e}[/red]")
                results[test_name] = False
            
            progress.remove_task(task)
    
    # 扫描大视频文件
    large_videos = scan_large_videos()
    
    # 显示测试结果总结
    console.print(f"\n{'='*60}")
    console.print("📊 [bold]测试结果总结[/bold]")
    console.print('='*60)
    
    passed_tests = sum(results.values())
    total_tests = len(results)
    
    for test_name, success in results.items():
        status = "✅ 通过" if success else "❌ 失败"
        console.print(f"  • {test_name}: {status}")
    
    # 显示依赖问题
    if dependency_issues:
        console.print(f"\n⚠️ [bold yellow]依赖问题:[/bold yellow]")
        for issue in dependency_issues:
            console.print(f"  • {issue}")
    
    # 显示大视频情况
    if large_videos:
        console.print(f"\n🎬 [bold]大视频文件状态:[/bold]")
        for video in large_videos:
            console.print(f"  • {video['path'].name}: {video['description']}")
    
    # 总结和建议
    if passed_tests == total_tests and not dependency_issues:
        console.print(f"\n🎉 [bold green]所有测试通过！({passed_tests}/{total_tests})[/bold green]")
        console.print("✨ [green]PyJianYin 大视频分析功能完全可用！[/green]")
        
        if large_videos:
            console.print(f"\n📋 [bold]下一步建议:[/bold]")
            console.print("  1. 运行 'python main.py analyze' 分析所有素材")
            console.print("  2. 大视频将自动分割并分析")
            console.print("  3. 查看 asset_manager/assets.json 获取结果")
        else:
            console.print(f"\n📋 [bold]建议:[/bold]")
            console.print("  1. 添加一些大于 20MB 的视频文件到素材目录")
            console.print("  2. 运行 'python main.py analyze' 测试实际分析")
    else:
        console.print(f"\n⚠️ [bold yellow]部分测试失败 ({passed_tests}/{total_tests})[/bold yellow]")
        
        if dependency_issues:
            console.print(f"\n🔧 [bold]修复建议:[/bold]")
            console.print("  1. 解决依赖问题")
            console.print("  2. 重新运行测试")
        
        console.print(f"\n📋 [bold]故障排除:[/bold]")
        console.print("  1. 检查网络连接")
        console.print("  2. 验证 API Key 配置")
        console.print("  3. 确保 FFmpeg 正确安装")
        console.print("  4. 查看详细错误信息")
    
    return passed_tests == total_tests and not dependency_issues

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
