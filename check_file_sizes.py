#!/usr/bin/env python3
"""
检查素材文件大小的工具
确保所有文件都符合新 API 的 20MB 限制
"""

import sys
from pathlib import Path
from rich.table import Table
from rich.panel import Panel
from rich.progress import Progress, SpinnerColumn, TextColumn

# 添加项目根目录到路径
sys.path.append(str(Path(__file__).parent))
from config import get_config
from console_utils import safe_console, print_success, print_error, print_warning, print_info

console = safe_console
config = get_config()

def format_file_size(size_bytes: int) -> str:
    """格式化文件大小显示"""
    if size_bytes < 1024:
        return f"{size_bytes} B"
    elif size_bytes < 1024 * 1024:
        return f"{size_bytes / 1024:.1f} KB"
    else:
        return f"{size_bytes / 1024 / 1024:.1f} MB"

def check_single_file(file_path: Path) -> dict:
    """检查单个文件的大小和状态"""
    try:
        file_size = file_path.stat().st_size
        size_mb = file_size / 1024 / 1024
        
        # 检查是否符合限制
        within_limit = file_size <= config.max_file_size
        
        # 检查是否为支持的格式
        is_supported = config.is_supported_file(str(file_path))
        
        return {
            "path": file_path,
            "size_bytes": file_size,
            "size_mb": size_mb,
            "size_formatted": format_file_size(file_size),
            "within_limit": within_limit,
            "is_supported": is_supported,
            "status": "SUCCESS:" if within_limit and is_supported else "ERROR:" if not within_limit else "WARNING:"
        }
    except Exception as e:
        return {
            "path": file_path,
            "size_bytes": 0,
            "size_mb": 0,
            "size_formatted": "错误",
            "within_limit": False,
            "is_supported": False,
            "status": "ERROR:",
            "error": str(e)
        }

def scan_assets_directory() -> list:
    """扫描素材目录中的所有文件"""
    try:
        assets_dir = config.assets_dir

        if not assets_dir.exists():
            console.print(f"[red]ERROR:[/red] 素材目录不存在: {assets_dir}")
            console.print(f"[yellow]提示:[/yellow] 请运行 'python main.py setup' 创建素材目录")
            return []

        console.print(f"[blue]扫描素材目录:[/blue] {assets_dir}")

        all_files = []

        with Progress(
            SpinnerColumn(),
            TextColumn("[progress.description]{task.description}"),
            console=console
        ) as progress:

            task = progress.add_task("扫描文件...", total=None)

            try:
                # 递归查找所有文件
                for file_path in assets_dir.rglob("*"):
                    if file_path.is_file():
                        file_info = check_single_file(file_path)
                        all_files.append(file_info)
            except Exception as e:
                console.print(f"ERROR: [red]扫描文件时出错: {e}[/red]")

            progress.remove_task(task)

        return all_files

    except Exception as e:
        console.print(f"ERROR: [red]扫描素材目录失败: {e}[/red]")
        return []

def analyze_files(files: list) -> dict:
    """分析文件统计信息"""
    stats = {
        "total_files": len(files),
        "supported_files": 0,
        "within_limit": 0,
        "over_limit": 0,
        "total_size": 0,
        "largest_file": None,
        "over_limit_files": []
    }
    
    for file_info in files:
        if file_info["is_supported"]:
            stats["supported_files"] += 1
            stats["total_size"] += file_info["size_bytes"]
            
            if file_info["within_limit"]:
                stats["within_limit"] += 1
            else:
                stats["over_limit"] += 1
                stats["over_limit_files"].append(file_info)
            
            # 记录最大文件
            if stats["largest_file"] is None or file_info["size_bytes"] > stats["largest_file"]["size_bytes"]:
                stats["largest_file"] = file_info
    
    return stats

def display_results(files: list, stats: dict):
    """显示检查结果"""
    
    # 显示统计信息
    console.print("\n[bold]文件大小统计[/bold]")

    stats_table = Table(show_header=True, header_style="bold blue")
    stats_table.add_column("项目", style="cyan")
    stats_table.add_column("数量/大小", style="white")
    stats_table.add_column("状态", style="green")

    stats_table.add_row("总文件数", str(stats["total_files"]), "")
    stats_table.add_row("支持的格式", str(stats["supported_files"]), "")
    stats_table.add_row("符合大小限制", str(stats["within_limit"]), "OK" if stats["over_limit"] == 0 else "WARN")
    stats_table.add_row("超过大小限制", str(stats["over_limit"]), "ERROR" if stats["over_limit"] > 0 else "OK")
    stats_table.add_row("总大小", format_file_size(stats["total_size"]), "")
    
    if stats["largest_file"]:
        largest = stats["largest_file"]
        stats_table.add_row("最大文件", f"{largest['path'].name} ({largest['size_formatted']})",
                           "OK" if largest["within_limit"] else "ERROR")
    
    console.print(stats_table)
    
    # 显示超过限制的文件
    if stats["over_limit_files"]:
        console.print(f"\n[bold red]超过 20MB 限制的文件 ({len(stats['over_limit_files'])} 个):[/bold red]")
        
        over_limit_table = Table(show_header=True, header_style="bold red")
        over_limit_table.add_column("文件名", style="white")
        over_limit_table.add_column("大小", style="red")
        over_limit_table.add_column("路径", style="dim")
        
        for file_info in sorted(stats["over_limit_files"], key=lambda x: x["size_bytes"], reverse=True):
            relative_path = file_info["path"].relative_to(config.assets_dir)
            over_limit_table.add_row(
                file_info["path"].name,
                file_info["size_formatted"],
                str(relative_path.parent) if relative_path.parent != Path(".") else "根目录"
            )
        
        console.print(over_limit_table)
        
        # 提供解决建议
        console.print("\nINFO: [bold blue]解决建议:[/bold blue]")
        console.print("  1. VIDEO: [cyan]自动分割分析[/cyan]: PyJianYin 可自动分割大视频进行分析")
        console.print("  2. 📉 压缩视频文件（降低分辨率或比特率）")
        console.print("  3. ✂️ 裁剪视频长度")
        console.print("  4. IMAGE: 压缩图片文件（降低质量或分辨率）")
        console.print("  5. FOLDER: 将大文件移动到其他目录")
        console.print("  6. PROGRESS: 使用视频编辑软件重新导出")

        # 检查是否有视频文件，提供分割分析选项
        video_files = [f for f in stats["over_limit_files"]
                      if Path(f["path"]).suffix.lower() in {'.mp4', '.mov', '.avi', '.mkv'}]

        if video_files:
            console.print(f"\nVIDEO: [bold cyan]视频分割分析选项:[/bold cyan]")
            console.print(f"  发现 {len(video_files)} 个大视频文件")
            console.print("  PyJianYin 可以自动分割这些视频并分次分析")
            console.print("  运行 'python main.py analyze' 时会自动处理大视频")
            console.print("  或运行 'python main.py test-large-video' 测试功能")

def suggest_compression():
    """提供压缩建议"""
    console.print("\n🛠️ [bold blue]文件压缩工具建议:[/bold blue]")
    
    tools_table = Table(show_header=True, header_style="bold blue")
    tools_table.add_column("工具", style="cyan")
    tools_table.add_column("用途", style="white")
    tools_table.add_column("命令示例", style="green")
    
    tools_table.add_row(
        "FFmpeg",
        "视频压缩",
        "ffmpeg -i input.mp4 -crf 28 -preset medium output.mp4"
    )
    tools_table.add_row(
        "ImageMagick",
        "图片压缩",
        "magick input.jpg -quality 80 -resize 1920x1080 output.jpg"
    )
    tools_table.add_row(
        "HandBrake",
        "视频压缩 (GUI)",
        "图形界面操作"
    )
    tools_table.add_row(
        "TinyPNG",
        "在线图片压缩",
        "https://tinypng.com/"
    )
    
    console.print(tools_table)

def main():
    """主函数"""
    console.print(Panel(
        "[bold blue]PyJianYin 文件大小检查工具[/bold blue]\n" +
        "检查素材文件是否符合新 API 的 20MB 限制",
        border_style="blue"
    ))
    
    # 扫描文件
    files = scan_assets_directory()
    
    if not files:
        console.print("📭 [yellow]未找到任何文件[/yellow]")
        return
    
    # 分析文件
    stats = analyze_files(files)
    
    # 显示结果
    display_results(files, stats)
    
    # 总结
    if stats["over_limit"] == 0:
        console.print("\n[bold green]所有支持的文件都符合 20MB 限制！[/bold green]")
        console.print("[green]可以正常使用新版 Gemini API 进行分析[/green]")
    else:
        console.print(f"\n[bold yellow]发现 {stats['over_limit']} 个文件超过 20MB 限制[/bold yellow]")
        console.print("[yellow]需要压缩这些文件才能使用新版 API[/yellow]")
        suggest_compression()

    # 显示下一步建议
    console.print("\n[bold]建议的下一步:[/bold]")
    if stats["over_limit"] > 0:
        console.print("  1. 压缩超过限制的文件")
        console.print("  2. 重新运行此检查工具")
        console.print("  3. 运行 'python main.py test-new-api' 测试 API")
    else:
        console.print("  1. 运行 'python main.py test-new-api' 测试新版 API")
        console.print("  2. 运行 'python main.py analyze' 分析素材")

if __name__ == "__main__":
    main()
