# 素材库说明

这个目录是 PyJianYin 的素材库，用于存放所有的视频、图片、音乐和音效文件。

## 目录结构

```
my_video_assets/
├── videos/           # 视频文件
│   ├── knowledge/    # 知识科普类视频
│   ├── economics/    # 经济相关视频
│   ├── funny/        # 搞笑娱乐视频
│   ├── technology/   # 科技类视频
│   ├── nature/       # 自然风景视频
│   └── business/     # 商务场景视频
├── images/           # 图片文件
│   ├── charts/       # 图表数据
│   ├── backgrounds/  # 背景图片
│   ├── icons/        # 图标素材
│   └── memes/        # 表情包梗图
├── music/            # 背景音乐
│   ├── calm/         # 平静音乐
│   ├── energetic/    # 活力音乐
│   └── serious/      # 严肃音乐
└── sound/            # 音效文件
    ├── transitions/  # 转场音效
    └── effects/      # 特效音效
```

## 素材要求

### 视频文件
- **格式**: MP4, MOV, AVI, MKV
- **分辨率**: 建议 1080p 或以上
- **时长**: 5-30秒的短片段效果最佳
- **内容**: 清晰、高质量的B-roll素材

### 图片文件
- **格式**: JPG, PNG, BMP
- **分辨率**: 建议 1920x1080 或以上
- **内容**: 图表、背景、图标等

### 音乐文件
- **格式**: MP3, WAV, AAC, M4A
- **质量**: 建议 320kbps 或以上
- **时长**: 1-5分钟

## 素材分类建议

### knowledge/ (知识科普)
- 科学实验视频
- 技术演示
- 教育场景
- 实验室环境
- 书籍、学习场景

### economics/ (经济金融)
- 股市图表动画
- 商务会议
- 工厂生产线
- 金融数据可视化
- 办公环境

### funny/ (搞笑娱乐)
- 动物搞笑视频
- 意外瞬间
- 表情包动图
- 网络梗素材
- 日常生活趣事

### technology/ (科技)
- 电子产品
- 代码编程
- 机器人
- 未来科技
- 数字化场景

### nature/ (自然)
- 风景延时
- 动物世界
- 天气现象
- 植物生长
- 自然美景

### business/ (商务)
- 会议场景
- 团队协作
- 商务谈判
- 办公环境
- 商业活动

## 使用说明

1. **添加素材**: 将素材文件放入对应的分类文件夹
2. **命名规范**: 使用英文命名，避免特殊字符和空格
3. **分析素材**: 运行 `python asset_manager/asset_analyzer.py` 分析所有素材
4. **查看结果**: 分析结果保存在 `asset_manager/assets.json`

## 版权声明

请确保所有素材都有合法的使用权限：
- 自己拍摄的原创素材
- 购买的正版素材
- 免费商用素材
- 已获得授权的素材

**注意**: 不要使用有版权争议的素材，避免法律风险。

## 推荐素材网站

### 免费素材
- Pixabay (https://pixabay.com/)
- Unsplash (https://unsplash.com/)
- Pexels (https://www.pexels.com/)
- Freepik (https://www.freepik.com/)

### 付费素材
- Shutterstock (https://www.shutterstock.com/)
- Getty Images (https://www.gettyimages.com/)
- Adobe Stock (https://stock.adobe.com/)
- VideoHive (https://videohive.net/)

## 素材优化建议

1. **文件大小**: 控制单个文件在 50MB 以内
2. **命名规范**: 使用描述性的英文名称
3. **质量标准**: 优先选择高清、无水印的素材
4. **多样性**: 每个分类准备 10-20 个不同的素材
5. **更新频率**: 定期添加新素材，保持库的新鲜度
