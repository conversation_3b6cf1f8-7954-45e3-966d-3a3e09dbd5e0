#!/usr/bin/env python3
"""
快速修复工具
帮助用户快速解决常见的网络和配置问题
"""

import sys
import os
import json
from pathlib import Path
from rich.console import Console
from rich.panel import Panel
from rich.prompt import Prompt, Confirm
from rich.table import Table

# 添加项目根目录到路径
sys.path.append(str(Path(__file__).parent))
from config import get_config

console = Console()
config = get_config()

def fix_network_timeout():
    """修复网络超时问题"""
    console.print("🔧 [bold blue]修复网络超时问题[/bold blue]")
    
    fixes = [
        {
            "name": "创建离线素材数据库",
            "description": "基于文件路径创建基础素材信息，不依赖网络",
            "action": "offline_mode"
        },
        {
            "name": "调整网络设置",
            "description": "修改配置以适应网络环境",
            "action": "network_config"
        },
        {
            "name": "清理缓存文件",
            "description": "删除可能损坏的缓存文件",
            "action": "clear_cache"
        }
    ]
    
    console.print("可用的修复方案：")
    for i, fix in enumerate(fixes, 1):
        console.print(f"  {i}. {fix['name']} - {fix['description']}")
    
    choice = Prompt.ask("请选择修复方案", choices=['1', '2', '3', 'all'], default='1')
    
    if choice == '1' or choice == 'all':
        create_offline_database()
    
    if choice == '2' or choice == 'all':
        adjust_network_config()
    
    if choice == '3' or choice == 'all':
        clear_cache_files()

def create_offline_database():
    """创建离线数据库"""
    console.print("\n📁 [bold]创建离线素材数据库...[/bold]")
    
    try:
        import subprocess
        result = subprocess.run([sys.executable, "offline_mode.py"], 
                              cwd=config.project_root,
                              capture_output=True,
                              text=True)
        
        if result.returncode == 0:
            console.print("✅ [green]离线数据库创建成功[/green]")
            return True
        else:
            console.print(f"❌ [red]创建失败: {result.stderr}[/red]")
            return False
            
    except Exception as e:
        console.print(f"❌ [red]创建失败: {e}[/red]")
        return False

def adjust_network_config():
    """调整网络配置"""
    console.print("\n🌐 [bold]调整网络配置...[/bold]")
    
    # 创建网络配置文件
    network_config = {
        "timeout": 60,
        "max_retries": 3,
        "retry_delay": 2,
        "max_file_size_mb": 50,
        "batch_size": 5
    }
    
    config_file = config.project_root / "network_config.json"
    
    try:
        with open(config_file, "w", encoding="utf-8") as f:
            json.dump(network_config, f, indent=2)
        
        console.print(f"✅ [green]网络配置已保存到: {config_file}[/green]")
        console.print("📋 配置内容:")
        for key, value in network_config.items():
            console.print(f"  • {key}: {value}")
        
        return True
        
    except Exception as e:
        console.print(f"❌ [red]配置保存失败: {e}[/red]")
        return False

def clear_cache_files():
    """清理缓存文件"""
    console.print("\n🗑️ [bold]清理缓存文件...[/bold]")
    
    cache_patterns = [
        "*.tmp",
        "*.cache",
        "__pycache__",
        ".pytest_cache",
        "*.pyc"
    ]
    
    cleaned_count = 0
    
    for pattern in cache_patterns:
        for file_path in config.project_root.rglob(pattern):
            try:
                if file_path.is_file():
                    file_path.unlink()
                    cleaned_count += 1
                elif file_path.is_dir():
                    import shutil
                    shutil.rmtree(file_path)
                    cleaned_count += 1
            except Exception as e:
                console.print(f"⚠️ [yellow]无法删除: {file_path} - {e}[/yellow]")
    
    console.print(f"✅ [green]清理完成，删除了 {cleaned_count} 个缓存文件[/green]")
    return True

def fix_api_key_issues():
    """修复 API Key 问题"""
    console.print("🔑 [bold blue]修复 API Key 问题[/bold blue]")
    
    env_file = config.project_root / ".env"
    env_example = config.project_root / ".env.example"
    
    # 检查 .env 文件是否存在
    if not env_file.exists():
        if env_example.exists():
            console.print("📝 创建 .env 文件...")
            import shutil
            shutil.copy(env_example, env_file)
            console.print("✅ [green].env 文件已创建[/green]")
        else:
            console.print("❌ [red].env.example 文件不存在[/red]")
            return False
    
    # 检查 API Key 配置
    if not config.validate_api_key():
        console.print("⚠️ [yellow]API Key 未配置或无效[/yellow]")
        
        if Confirm.ask("是否要配置 API Key？"):
            api_key = Prompt.ask("请输入你的 Gemini API Key", password=True)
            
            if api_key and api_key != "YOUR_API_KEY":
                try:
                    # 更新 .env 文件
                    with open(env_file, "r", encoding="utf-8") as f:
                        content = f.read()
                    
                    # 替换 API Key
                    if "GEMINI_API_KEY=" in content:
                        content = content.replace(
                            f"GEMINI_API_KEY={config.gemini_api_key}",
                            f"GEMINI_API_KEY={api_key}"
                        )
                    else:
                        content += f"\nGEMINI_API_KEY={api_key}\n"
                    
                    with open(env_file, "w", encoding="utf-8") as f:
                        f.write(content)
                    
                    console.print("✅ [green]API Key 已更新[/green]")
                    console.print("💡 [blue]请重启程序以使配置生效[/blue]")
                    return True
                    
                except Exception as e:
                    console.print(f"❌ [red]更新失败: {e}[/red]")
                    return False
            else:
                console.print("❌ [red]无效的 API Key[/red]")
                return False
    else:
        console.print("✅ [green]API Key 配置正常[/green]")
        return True

def fix_file_permissions():
    """修复文件权限问题"""
    console.print("📁 [bold blue]检查文件权限...[/bold blue]")
    
    important_dirs = [
        config.assets_dir,
        config.get_output_path(),
        config.get_examples_path(),
        config.project_root / "asset_manager",
        config.project_root / "storyboard_generator",
        config.project_root / "draft_builder",
        config.project_root / "export_controller"
    ]
    
    issues_found = False
    
    for dir_path in important_dirs:
        if not dir_path.exists():
            try:
                dir_path.mkdir(parents=True, exist_ok=True)
                console.print(f"✅ 创建目录: {dir_path}")
            except Exception as e:
                console.print(f"❌ [red]无法创建目录: {dir_path} - {e}[/red]")
                issues_found = True
        else:
            # 检查读写权限
            if not os.access(dir_path, os.R_OK | os.W_OK):
                console.print(f"⚠️ [yellow]权限不足: {dir_path}[/yellow]")
                issues_found = True
    
    if not issues_found:
        console.print("✅ [green]文件权限正常[/green]")
    
    return not issues_found

def show_system_info():
    """显示系统信息"""
    console.print("📊 [bold blue]系统信息[/bold blue]")
    
    import platform
    import sys
    
    info_table = Table(title="系统环境")
    info_table.add_column("项目", style="cyan")
    info_table.add_column("值", style="magenta")
    
    info_table.add_row("操作系统", platform.system())
    info_table.add_row("系统版本", platform.version())
    info_table.add_row("Python 版本", sys.version.split()[0])
    info_table.add_row("项目路径", str(config.project_root))
    info_table.add_row("API Key 状态", "已配置" if config.validate_api_key() else "未配置")
    
    # 检查网络代理
    proxy_status = "未设置"
    if any(key in os.environ for key in ['HTTP_PROXY', 'HTTPS_PROXY', 'http_proxy', 'https_proxy']):
        proxy_status = "已设置"
    info_table.add_row("网络代理", proxy_status)
    
    console.print(info_table)

def main():
    """主修复函数"""
    console.print(Panel(
        "[bold blue]PyJianYin 快速修复工具[/bold blue]\n" +
        "自动诊断和修复常见问题",
        border_style="blue"
    ))
    
    # 显示系统信息
    show_system_info()
    
    # 修复选项
    fixes = [
        ("网络超时问题", fix_network_timeout),
        ("API Key 问题", fix_api_key_issues),
        ("文件权限问题", fix_file_permissions),
    ]
    
    console.print("\n🔧 [bold]可用的修复选项:[/bold]")
    for i, (name, _) in enumerate(fixes, 1):
        console.print(f"  {i}. {name}")
    console.print("  a. 全部修复")
    console.print("  q. 退出")
    
    choice = Prompt.ask("请选择修复选项", default="1")
    
    if choice.lower() == 'q':
        console.print("👋 退出修复工具")
        return
    
    success_count = 0
    
    if choice.lower() == 'a':
        # 执行所有修复
        for name, fix_func in fixes:
            console.print(f"\n{'='*50}")
            console.print(f"🔧 [bold]执行: {name}[/bold]")
            console.print('='*50)
            
            if fix_func():
                success_count += 1
                console.print(f"✅ [green]{name} 修复成功[/green]")
            else:
                console.print(f"❌ [red]{name} 修复失败[/red]")
    else:
        # 执行单个修复
        try:
            fix_index = int(choice) - 1
            if 0 <= fix_index < len(fixes):
                name, fix_func = fixes[fix_index]
                console.print(f"\n🔧 [bold]执行: {name}[/bold]")
                
                if fix_func():
                    success_count = 1
                    console.print(f"✅ [green]{name} 修复成功[/green]")
                else:
                    console.print(f"❌ [red]{name} 修复失败[/red]")
            else:
                console.print("❌ [red]无效的选择[/red]")
        except ValueError:
            console.print("❌ [red]无效的选择[/red]")
    
    # 显示修复结果
    console.print(f"\n{'='*50}")
    console.print("📋 [bold]修复结果总结[/bold]")
    console.print('='*50)
    
    if success_count > 0:
        console.print(f"🎉 [green]成功修复 {success_count} 个问题[/green]")
        console.print("\n📋 [bold]建议的下一步:[/bold]")
        console.print("  1. 重启程序以使配置生效")
        console.print("  2. 运行 'python main.py status' 检查状态")
        console.print("  3. 尝试运行 'python main.py analyze' 分析素材")
    else:
        console.print("⚠️ [yellow]未能修复问题，请手动检查配置[/yellow]")
        console.print("\n💡 [blue]获取帮助:[/blue]")
        console.print("  • 查看 README.md 文档")
        console.print("  • 运行 'python main.py diagnose' 进行详细诊断")
        console.print("  • 在 GitHub 上提交 Issue")

if __name__ == "__main__":
    main()
