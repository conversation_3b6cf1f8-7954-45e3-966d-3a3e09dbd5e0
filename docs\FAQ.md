# PyJianYin 常见问题解答 (FAQ)

## 🚀 快速开始

### Q: 如何快速体验 PyJianYin？
A: 按照以下步骤：
1. 运行 `python main.py setup` 初始化环境
2. 配置 `.env` 文件中的 Gemini API Key
3. 在 `my_video_assets` 目录下放入一些测试素材
4. 运行 `python main.py auto` 开始一键生成

### Q: 需要什么前置条件？
A: 
- Python 3.10 或更高版本
- Gemini API Key（免费获取）
- 一些视频/图片素材
- （可选）剪映软件用于最终导出

## 🔑 API 配置

### Q: 如何获取 Gemini API Key？
A: 
1. 访问 [Google AI Studio](https://makersuite.google.com/app/apikey)
2. 使用 Google 账号登录
3. 点击 "Create API Key" 创建新密钥
4. 复制密钥到 `.env` 文件中

### Q: Gemini API 是免费的吗？
A: 是的，Gemini API 提供免费额度，足够个人用户使用。具体限制请查看 Google AI 官方文档。

### Q: API Key 配置后仍然报错？
A: 请检查：
- API Key 是否正确复制（没有多余空格）
- 网络连接是否正常
- 是否在支持的地区（某些地区可能无法访问）

## 📁 素材管理

### Q: 支持哪些文件格式？
A: 
- **视频**: MP4, MOV, AVI, MKV, WMV
- **图片**: JPG, JPEG, PNG, BMP, GIF
- **音频**: MP3, WAV, AAC, M4A, FLAC

### Q: 素材文件有大小限制吗？
A: 建议单个文件不超过 50MB，以确保上传和分析速度。

### Q: 如何组织素材文件夹？
A: 建议按内容类型分类：
```
my_video_assets/
├── videos/
│   ├── knowledge/    # 知识科普
│   ├── funny/        # 搞笑内容
│   └── business/     # 商务场景
├── images/
│   ├── charts/       # 图表
│   └── backgrounds/  # 背景
└── music/            # 音乐
```

### Q: 素材分析很慢怎么办？
A: 
- 确保网络连接稳定
- 减少单次分析的文件数量
- 使用 `--force` 参数只在必要时重新分析
- 考虑压缩大文件

## 🎬 视频生成

### Q: 如何选择合适的视频模式？
A: 
- **知识科普**: 教育内容，节奏平稳
- **经济解读**: 财经分析，严肃专业
- **搞笑卡点**: 娱乐内容，快节奏
- **主流视频二创**: 基于现有视频的再创作

### Q: 生成的剧本不满意怎么办？
A: 
- 调整视频主题描述，使其更具体
- 检查素材标签是否准确
- 尝试不同的视频模式
- 手动编辑生成的 storyboard.json 文件

### Q: 如何提高素材匹配准确性？
A: 
- 使用描述性的文件名
- 确保素材内容与文件夹分类一致
- 增加相关标签的素材数量
- 定期更新素材库

## 🎵 音乐和字幕

### Q: 如何制作 SRT 字幕文件？
A: 
- 使用字幕编辑软件（如 Subtitle Edit）
- 在线字幕生成工具
- 手动创建，格式如下：
```srt
1
00:00:00,000 --> 00:00:03,500
第一句字幕内容

2
00:00:03,500 --> 00:00:07,000
第二句字幕内容
```

### Q: 音乐文件有什么要求？
A: 
- 格式：MP3, WAV 等常见格式
- 时长：建议 1-5 分钟
- 质量：建议 320kbps 或更高
- 内容：选择与视频主题匹配的音乐

### Q: 搞笑卡点模式如何选择音乐？
A: 
- 选择节拍明显的音乐（120+ BPM）
- 避免过于复杂的音乐
- 确保音乐有清晰的节拍点
- 可以使用专门的卡点音乐

## 🛠️ 技术问题

### Q: 安装依赖时出错？
A: 
- 确保使用 Python 3.10+
- 尝试升级 pip: `pip install --upgrade pip`
- 使用虚拟环境避免冲突
- 检查网络连接

### Q: 运行时出现模块导入错误？
A: 
- 确保在项目根目录运行
- 检查 Python 路径设置
- 重新安装依赖包
- 使用 `python -m pip install -r requirements.txt`

### Q: 内存不足怎么办？
A: 
- 减少同时处理的文件数量
- 关闭其他占用内存的程序
- 使用较小的素材文件
- 分批处理大量素材

## 📤 导出问题

### Q: 导出功能不工作？
A: 导出功能需要：
- Windows 操作系统
- 剪映 6.0 或更低版本
- 剪映软件已打开并在主页
- 关闭可能干扰的其他软件

### Q: 可以在 Mac/Linux 上使用吗？
A: 
- 素材分析、剧本生成、草稿构建功能完全支持
- 自动导出功能仅支持 Windows + 剪映
- Mac/Linux 用户可以手动导入草稿到剪映

### Q: 如何手动导入草稿？
A: 
1. 将生成的 `draft_content.json` 复制到剪映草稿目录
2. 打开剪映，在草稿列表中找到新草稿
3. 手动调整和导出

## 🔧 自定义配置

### Q: 如何修改默认设置？
A: 编辑 `config.py` 文件，可以修改：
- 支持的文件格式
- 视频模式配置
- 默认路径设置
- 导出参数

### Q: 如何添加新的视频模式？
A: 在 `config.py` 的 `video_modes` 字典中添加新模式：
```python
"新模式名": {
    "description": "模式描述",
    "preferred_tags": ["标签1", "标签2"],
    "transitions": ["转场1", "转场2"],
    "pace": "节奏"
}
```

### Q: 如何自定义 AI 提示词？
A: 修改 `storyboard_generator.py` 中的 `build_mode_specific_prompt` 函数。

## 🐛 故障排除

### Q: 程序崩溃怎么办？
A: 
1. 查看错误信息
2. 检查日志文件
3. 尝试重新运行
4. 提交 Issue 并附上错误信息

### Q: 生成结果不理想？
A: 
- 检查素材质量和分类
- 调整视频主题描述
- 尝试不同的参数组合
- 手动编辑生成的文件

### Q: 如何重置所有设置？
A: 
1. 删除 `.env` 文件
2. 删除 `asset_manager/assets.json`
3. 运行 `python main.py setup` 重新初始化

## 📈 性能优化

### Q: 如何提高生成速度？
A: 
- 使用 `--skip-analyze` 跳过重复分析
- 准备高质量的素材和字幕
- 使用 SSD 存储
- 确保网络连接稳定

### Q: 如何减少内存使用？
A: 
- 分批处理大量文件
- 定期清理临时文件
- 使用较小的素材文件
- 关闭不必要的程序

## 💡 最佳实践

### Q: 如何获得最佳效果？
A: 
1. **素材准备**: 高质量、分类清晰的素材
2. **主题描述**: 具体、清晰的视频主题
3. **字幕质量**: 准确、完整的字幕文件
4. **音乐选择**: 与主题匹配的背景音乐
5. **模式选择**: 根据内容选择合适的视频模式

### Q: 如何提高 AI 分析准确性？
A: 
- 使用高质量的素材
- 确保文件名具有描述性
- 按内容类型组织文件夹
- 定期更新和清理素材库

## 🤝 社区支持

### Q: 如何获得帮助？
A: 
1. 查看本 FAQ 文档
2. 搜索 GitHub Issues
3. 创建新的 Issue
4. 参与 GitHub Discussions

### Q: 如何贡献代码？
A: 
1. Fork 项目
2. 创建功能分支
3. 提交 Pull Request
4. 参与代码审查

### Q: 如何报告 Bug？
A: 在 GitHub Issues 中提交，请包含：
- 详细的错误描述
- 复现步骤
- 系统环境信息
- 相关日志文件
