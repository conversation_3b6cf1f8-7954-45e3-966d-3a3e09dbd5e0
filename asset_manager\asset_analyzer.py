import os
import json
import sys
import time
import requests
from pathlib import Path
from pymediainfo import MediaInfo
from rich.console import Console
from rich.progress import Progress, TaskID
from rich.table import Table
from typing import Optional
from PIL import Image

# 添加项目根目录到路径
sys.path.append(str(Path(__file__).parent.parent))
from config import get_config

# 尝试导入大视频分析相关模块
try:
    from large_video_analyzer import LargeVideoAnalyzer
    from video_splitter import check_ffmpeg_availability, estimate_split_count
    LARGE_VIDEO_SUPPORT = True
except ImportError as e:
    console.print(f"⚠️ [yellow]大视频分析模块导入失败: {e}[/yellow]")
    LARGE_VIDEO_SUPPORT = False

# 初始化配置和控制台
config = get_config()
console = Console()

# 初始化 Gemini 客户端
try:
    from google import genai
    from google.genai import types

    # 设置 API Key
    if not config.validate_api_key():
        console.print("❌ [red]错误: 请先配置 GEMINI_API_KEY 环境变量[/red]")
        console.print("💡 [yellow]提示: 复制 .env.example 为 .env 并填入你的 API Key[/yellow]")
        sys.exit(1)

    os.environ['GOOGLE_API_KEY'] = config.gemini_api_key
    gemini_client = genai.Client()
    gemini_types = types
    console.print("✅ [green]新版 Google GenAI SDK 初始化成功[/green]")

except ImportError:
    console.print("❌ [red]新版 Google GenAI SDK 未安装[/red]")
    console.print("💡 [yellow]请运行: pip install google-genai[/yellow]")
    sys.exit(1)
except Exception as e:
    console.print(f"❌ [red]Gemini SDK 初始化失败: {e}[/red]")
    sys.exit(1)

# 网络配置
MAX_RETRIES = 3
RETRY_DELAY = 2  # 秒
UPLOAD_TIMEOUT = 60  # 秒
MAX_FILE_SIZE = 20 * 1024 * 1024  # 20MB - 新 API 的文件大小限制

def convert_image_to_png(image_path: str) -> str:
    """
    将图片转换为 PNG 格式（如果不是 PNG、JPG、JPEG 格式）

    Args:
        image_path: 原始图片路径

    Returns:
        str: 转换后的图片路径（如果需要转换）或原始路径
    """
    file_path = Path(image_path)
    ext = file_path.suffix.lower()

    # 如果已经是支持的格式，直接返回
    if ext in {'.png', '.jpg', '.jpeg'}:
        return image_path

    # 需要转换的格式
    if ext in {'.webp', '.bmp', '.gif'}:
        try:
            # 创建转换后的文件路径
            converted_path = file_path.parent / f"{file_path.stem}_converted.png"

            # 如果转换后的文件已存在且比原文件新，直接使用
            if converted_path.exists():
                original_mtime = file_path.stat().st_mtime
                converted_mtime = converted_path.stat().st_mtime
                if converted_mtime >= original_mtime:
                    console.print(f"📷 [cyan]使用已转换的图片: {converted_path.name}[/cyan]")
                    return str(converted_path)

            # 执行转换
            console.print(f"🔄 [yellow]转换图片格式: {file_path.name} -> PNG[/yellow]")

            with Image.open(image_path) as img:
                # 如果图片有透明通道，保持 RGBA 模式
                if img.mode in ('RGBA', 'LA') or (img.mode == 'P' and 'transparency' in img.info):
                    img = img.convert('RGBA')
                else:
                    img = img.convert('RGB')

                # 保存为 PNG
                img.save(converted_path, 'PNG', optimize=True)

            console.print(f"✅ [green]图片转换成功: {converted_path.name}[/green]")
            return str(converted_path)

        except Exception as e:
            console.print(f"❌ [red]图片转换失败: {file_path.name} - {e}[/red]")
            return image_path  # 转换失败时返回原路径

    # 不支持的格式，返回原路径
    return image_path

def get_mime_type(file_path: str) -> str:
    """根据文件扩展名获取 MIME 类型"""
    ext = Path(file_path).suffix.lower()
    mime_types = {
        '.png': 'image/png',
        '.jpg': 'image/jpeg',
        '.jpeg': 'image/jpeg',
        '.gif': 'image/gif',
        '.bmp': 'image/bmp',
        '.webp': 'image/webp',
        '.mp4': 'video/mp4',
        '.mov': 'video/quicktime',
        '.avi': 'video/x-msvideo',
        '.mkv': 'video/x-matroska',
        '.mp3': 'audio/mpeg',
        '.wav': 'audio/wav',
        '.txt': 'text/plain',
    }
    return mime_types.get(ext, 'application/octet-stream')

def analyze_large_video(file_path: str, size_info: str) -> Optional[dict]:
    """
    分析大视频文件（超过20MB）

    Args:
        file_path: 视频文件路径
        size_info: 文件大小信息

    Returns:
        dict: 分析结果，失败时返回 None
    """
    console.print(f"🎬 [bold yellow]检测到大视频文件: {Path(file_path).name} ({size_info})[/bold yellow]")

    # 检查大视频分析模块是否可用
    if not LARGE_VIDEO_SUPPORT:
        console.print("❌ [red]大视频分析模块不可用[/red]")
        console.print("💡 [yellow]请压缩视频文件到20MB以下[/yellow]")
        return None

    # 检查 FFmpeg 是否可用
    if not check_ffmpeg_availability():
        console.print("❌ [red]FFmpeg 未安装，无法分割大视频[/red]")
        console.print("💡 [yellow]请安装 FFmpeg 或压缩视频文件到20MB以下[/yellow]")
        return None

    # 估算分割情况
    needs_split, segment_count, description = estimate_split_count(file_path)
    console.print(f"📊 分割预估: {description}")

    if segment_count > 10:
        console.print(f"⚠️ [yellow]视频过大，预估需要分割为 {segment_count} 段，建议先压缩[/yellow]")
        console.print("💡 [yellow]可使用: ffmpeg -i input.mp4 -crf 28 -preset medium output.mp4[/yellow]")
        return None

    try:
        # 使用大视频分析器
        analyzer = LargeVideoAnalyzer()
        result = analyzer.analyze_large_video(file_path)

        if result:
            console.print(f"✅ [green]大视频分析成功: {Path(file_path).name}[/green]")
            return result
        else:
            console.print(f"❌ [red]大视频分析失败: {Path(file_path).name}[/red]")
            return None

    except Exception as e:
        console.print(f"❌ [red]大视频分析异常: {Path(file_path).name} - {e}[/red]")
        return None

def check_network_connection() -> bool:
    """检查网络连接"""
    try:
        response = requests.get("https://www.google.com", timeout=5)
        return response.status_code == 200
    except:
        return False

def analyze_media_with_retry(file_path: str, progress: Progress = None, task_id: TaskID = None) -> Optional[dict]:
    """
    带重试机制的媒体文件分析

    Args:
        file_path: 媒体文件路径
        progress: 进度条对象
        task_id: 任务ID

    Returns:
        dict: 包含描述、标签、情绪、类别等信息，失败时返回 None
    """
    file_name = Path(file_path).name

    for attempt in range(MAX_RETRIES):
        try:
            if progress and task_id:
                retry_info = f" (重试 {attempt + 1}/{MAX_RETRIES})" if attempt > 0 else ""
                progress.update(task_id, description=f"分析: {file_name}{retry_info}")

            result = analyze_media_single(file_path)
            if result:
                return result

        except Exception as e:
            error_msg = str(e)
            if "10060" in error_msg or "timeout" in error_msg.lower():
                if attempt < MAX_RETRIES - 1:
                    console.print(f"⚠️ [yellow]网络超时，{RETRY_DELAY}秒后重试: {file_name}[/yellow]")
                    time.sleep(RETRY_DELAY)
                    continue
                else:
                    console.print(f"❌ [red]网络连接失败，跳过: {file_name}[/red]")
            else:
                console.print(f"❌ [red]分析失败: {file_name} - {error_msg}[/red]")
            break

    return None

def analyze_media_single(file_path: str) -> Optional[dict]:
    """
    分析单个媒体文件（无重试）

    Args:
        file_path: 媒体文件路径

    Returns:
        dict: 包含描述、标签、情绪、类别等信息，失败时返回 None
    """
    # 检查文件是否存在和格式是否支持
    if not Path(file_path).exists():
        console.print(f"❌ [red]文件不存在: {file_path}[/red]")
        return None

    if not config.is_supported_file(file_path):
        console.print(f"⚠️ [yellow]不支持的文件格式: {file_path}[/yellow]")
        return None

    # 检查文件大小
    size_ok, size_info = config.check_file_size(file_path)

    # 如果是视频文件且超过20MB，尝试使用分割分析
    if not size_ok and config.is_supported_file(file_path, "video"):
        return analyze_large_video(file_path, size_info)
    elif not size_ok:
        console.print(f"⚠️ [yellow]文件过大，跳过: {Path(file_path).name} ({size_info})[/yellow]")
        return None

    # 如果是图片文件，检查是否需要格式转换
    actual_file_path = file_path
    if config.is_supported_file(file_path, "image"):
        actual_file_path = convert_image_to_png(file_path)

    # 读取文件数据（新 API 方式）
    try:
        with open(actual_file_path, 'rb') as f:
            file_data = f.read()

        # 确定 MIME 类型（使用转换后的文件）
        mime_type = get_mime_type(actual_file_path)

    except Exception as e:
        console.print(f"❌ [red]文件读取失败: {Path(file_path).name} - {e}[/red]")
        return None

    # 设计强大的 Prompt
    prompt_text = """
    Analyze this media file and provide a JSON output with the following keys:
    - "description": A concise, objective description of the content in Chinese.
    - "tags": A list of relevant keywords in English (e.g., "technology", "nature", "fast-paced", "calm", "B-roll").
    - "mood": The overall mood in English (e.g., "inspirational", "humorous", "serious", "techy").
    - "category": Based on its content, suggest a category like "knowledge", "economics", "funny_moment", "transition_effect".
    - "quality": Rate the visual/audio quality as "high", "medium", or "low".
    - "duration_category": For videos, categorize as "short" (<5s), "medium" (5-30s), or "long" (>30s).

    Example for a video of a rocket launch:
    {
      "description": "火箭从发射台升空的特写镜头，伴随着巨大的烟雾和火焰",
      "tags": ["space", "rocket", "technology", "launch", "powerful", "inspirational"],
      "mood": "inspirational",
      "category": "knowledge",
      "quality": "high",
      "duration_category": "medium"
    }

    Please respond with valid JSON only.
    """

    try:
        # 使用新 API 的多模态内容格式
        media_part = gemini_types.Part(
            inline_data=gemini_types.Blob(
                data=file_data,
                mime_type=mime_type
            )
        )

        content = gemini_types.Content(parts=[
            gemini_types.Part(text=prompt_text),
            media_part
        ])

        response = gemini_client.models.generate_content(
            model=f'models/{config.gemini_model_pro}',
            contents=content
        )

        if not response or not response.text:
            console.print(f"❌ [red]AI 分析失败: {Path(file_path).name}[/red]")
            return None

        # 清理和解析返回的JSON
        cleaned_text = response.text.strip()
        # 移除可能的 markdown 代码块标记
        cleaned_text = cleaned_text.replace('```json', '').replace('```', '').strip()

        try:
            result = json.loads(cleaned_text)
            return result
        except json.JSONDecodeError as e:
            console.print(f"❌ [red]JSON 解析失败: {Path(file_path).name}[/red]")
            console.print(f"原始响应: {cleaned_text[:200]}...")
            return None

    except Exception as e:
        console.print(f"❌ [red]API 调用失败: {Path(file_path).name} - {e}[/red]")
        return None

# 保持向后兼容
def analyze_media(file_path: str, progress: Progress = None, task_id: TaskID = None) -> Optional[dict]:
    """分析媒体文件的主要接口"""
    return analyze_media_with_retry(file_path, progress, task_id)

def get_media_info(file_path: str) -> dict:
    """获取媒体文件的基本信息"""
    info = {"duration_ms": None, "width": None, "height": None, "file_size": None}

    try:
        # 获取文件大小
        info["file_size"] = Path(file_path).stat().st_size

        # 获取媒体信息
        media_info = MediaInfo.parse(file_path)
        for track in media_info.tracks:
            if track.track_type == "Video":
                info["duration_ms"] = track.duration
                info["width"] = track.width
                info["height"] = track.height
                break
    except Exception as e:
        console.print(f"⚠️ [yellow]无法获取媒体信息: {Path(file_path).name} - {str(e)}[/yellow]")

    return info

def analyze_all_assets(root_dir: str = None, output_json: str = None, force_reanalyze: bool = False,
                      skip_network_check: bool = False) -> bool:
    """
    遍历素材文件夹，分析所有媒体文件，生成 assets.json

    Args:
        root_dir: 素材根目录，默认使用配置中的路径
        output_json: 输出的 JSON 文件名，默认使用配置中的路径
        force_reanalyze: 是否强制重新分析已存在的文件
        skip_network_check: 是否跳过网络检查

    Returns:
        bool: 是否成功完成分析
    """
    if root_dir is None:
        root_dir = config.assets_dir
    if output_json is None:
        output_json = config.assets_json

    root_path = Path(root_dir)
    output_path = Path(output_json)

    if not root_path.exists():
        console.print(f"❌ [red]素材目录不存在: {root_path}[/red]")
        return False

    # 检查网络连接
    if not skip_network_check:
        console.print("🌐 检查网络连接...")
        if not check_network_connection():
            console.print("❌ [red]网络连接失败，无法访问 Gemini API[/red]")
            console.print("💡 [yellow]请检查网络连接或使用 --skip-network-check 跳过此检查[/yellow]")
            return False
        console.print("✅ 网络连接正常")

    # 确保输出目录存在
    output_path.parent.mkdir(parents=True, exist_ok=True)

    # 收集所有媒体文件
    media_files = []
    for file_path in root_path.rglob("*"):
        if file_path.is_file() and config.is_supported_file(str(file_path)):
            # 检查文件大小
            size_ok, size_info = config.check_file_size(str(file_path))
            if not size_ok:
                # 如果是视频文件，可以通过分割处理
                if config.is_supported_file(str(file_path), "video"):
                    console.print(f"📹 [cyan]发现大视频文件: {file_path.name} ({size_info}) - 将使用分割分析[/cyan]")
                    media_files.append(file_path)
                else:
                    console.print(f"⚠️ [yellow]跳过大文件: {file_path.name} ({size_info})[/yellow]")
                    continue
            else:
                media_files.append(file_path)

    if not media_files:
        console.print(f"⚠️ [yellow]在 {root_path} 中未找到支持的媒体文件[/yellow]")
        return False

    console.print(f"📁 找到 {len(media_files)} 个媒体文件")

    # 加载已有的分析结果
    existing_assets = {}
    if output_path.exists() and not force_reanalyze:
        try:
            with open(output_path, 'r', encoding='utf-8') as f:
                existing_data = json.load(f)
                for asset in existing_data:
                    existing_assets[asset['path']] = asset
            console.print(f"📄 加载了 {len(existing_assets)} 个已有分析结果")
        except Exception as e:
            console.print(f"⚠️ [yellow]无法加载已有分析结果: {e}[/yellow]")

    assets = []
    failed_count = 0

    with Progress() as progress:
        task = progress.add_task("分析素材文件...", total=len(media_files))

        for i, file_path in enumerate(media_files):
            file_str = str(file_path)

            # 检查是否已经分析过
            if file_str in existing_assets and not force_reanalyze:
                assets.append(existing_assets[file_str])
                progress.advance(task)
                continue

            # 确定文件类型
            file_type = "image"
            if file_path.suffix.lower() in config.supported_video_formats:
                file_type = "video"

            # 获取媒体信息
            media_info = get_media_info(file_str)

            # AI 分析
            analysis = analyze_media(file_str, progress, task)

            if analysis:
                asset_data = {
                    "path": file_str,
                    "type": file_type,
                    "analysis": analysis,
                    **media_info,
                    "analyzed_at": str(Path(file_str).stat().st_mtime)
                }
                assets.append(asset_data)
            else:
                failed_count += 1
                # 如果连续失败太多，询问是否继续
                if failed_count >= 3 and i < len(media_files) - 1:
                    console.print(f"\n⚠️ [yellow]已有 {failed_count} 个文件分析失败[/yellow]")
                    console.print("💡 [blue]建议:[/blue]")
                    console.print("  1. 检查网络连接是否稳定")
                    console.print("  2. 减少文件大小")
                    console.print("  3. 稍后重试")

                    from rich.prompt import Confirm
                    if not Confirm.ask("是否继续分析剩余文件？"):
                        break
                    failed_count = 0  # 重置计数器

            progress.advance(task)

    # 保存结果
    try:
        with open(output_path, "w", encoding="utf-8") as f:
            json.dump(assets, f, ensure_ascii=False, indent=2)

        console.print(f"✅ [green]分析完成！结果已保存到 {output_path}[/green]")
        console.print(f"📊 成功分析: {len(assets)} 个文件")
        if failed_count > 0:
            console.print(f"⚠️ [yellow]失败: {failed_count} 个文件[/yellow]")

        # 显示统计信息
        if assets:
            show_analysis_summary(assets)

        return len(assets) > 0

    except Exception as e:
        console.print(f"❌ [red]保存失败: {e}[/red]")
        return False

def show_analysis_summary(assets: list):
    """显示分析结果摘要"""
    if not assets:
        return

    # 统计信息
    stats = {
        "total": len(assets),
        "videos": sum(1 for a in assets if a["type"] == "video"),
        "images": sum(1 for a in assets if a["type"] == "image"),
        "categories": {},
        "moods": {}
    }

    for asset in assets:
        analysis = asset.get("analysis", {})
        category = analysis.get("category", "unknown")
        mood = analysis.get("mood", "unknown")

        stats["categories"][category] = stats["categories"].get(category, 0) + 1
        stats["moods"][mood] = stats["moods"].get(mood, 0) + 1

    # 创建统计表格
    table = Table(title="📊 素材分析摘要")
    table.add_column("类型", style="cyan")
    table.add_column("数量", style="magenta")

    table.add_row("总文件数", str(stats["total"]))
    table.add_row("视频文件", str(stats["videos"]))
    table.add_row("图片文件", str(stats["images"]))

    console.print(table)

    # 显示分类统计
    if stats["categories"]:
        console.print("\n📂 [bold]内容分类:[/bold]")
        for category, count in sorted(stats["categories"].items(), key=lambda x: x[1], reverse=True):
            console.print(f"  • {category}: {count}")

    if stats["moods"]:
        console.print("\n🎭 [bold]情绪分布:[/bold]")
        for mood, count in sorted(stats["moods"].items(), key=lambda x: x[1], reverse=True):
            console.print(f"  • {mood}: {count}")

if __name__ == "__main__":
    console.print("🚀 [bold blue]PyJianYin 素材分析器[/bold blue]")

    # 检查配置
    if not config.validate_api_key():
        console.print("❌ [red]请先配置 GEMINI_API_KEY[/red]")
        sys.exit(1)

    # 分析素材
    success = analyze_all_assets()
    if success:
        console.print("🎉 [green]素材分析完成！[/green]")
    else:
        console.print("💥 [red]素材分析失败[/red]")
        sys.exit(1)