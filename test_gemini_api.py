#!/usr/bin/env python3
"""
Gemini API 功能测试脚本
测试 Gemini API 处理文字、图片和视频的能力
"""

import sys
import os
import time
import tempfile
from pathlib import Path
from rich.console import Console
from rich.panel import Panel
from rich.progress import Progress, SpinnerColumn, TextColumn
from rich.table import Table

# 添加项目根目录到路径
sys.path.append(str(Path(__file__).parent))
from config import get_config
from gemini_adapter import get_gemini_adapter

console = Console()
config = get_config()

def create_test_image() -> str:
    """创建测试图片（使用现有素材或创建简单图片）"""
    try:
        # 尝试使用 PIL 创建测试图片
        from PIL import Image, ImageDraw

        # 创建一个简单的测试图片
        img = Image.new('RGB', (400, 300), color='lightblue')
        draw = ImageDraw.Draw(img)

        # 绘制一些内容
        draw.rectangle([50, 50, 350, 250], fill='white', outline='black', width=2)
        draw.text((100, 100), "测试图片", fill='black')
        draw.text((100, 130), "Test Image", fill='black')
        draw.ellipse([200, 160, 280, 220], fill='red', outline='darkred', width=2)

        # 保存到临时文件
        temp_file = tempfile.NamedTemporaryFile(suffix='.png', delete=False)
        img.save(temp_file.name, 'PNG')
        return temp_file.name

    except ImportError:
        # 如果 PIL 不可用，尝试使用现有的图片文件
        config = get_config()
        for img_path in config.assets_dir.rglob("*.png"):
            if img_path.exists():
                console.print(f"📁 使用现有图片: {img_path.name}")
                return str(img_path)

        for img_path in config.assets_dir.rglob("*.jpg"):
            if img_path.exists():
                console.print(f"📁 使用现有图片: {img_path.name}")
                return str(img_path)

        console.print("⚠️ [yellow]无法创建或找到测试图片[/yellow]")
        return None

def create_test_video() -> str:
    """创建测试视频或使用现有视频"""
    try:
        # 尝试使用 OpenCV 创建测试视频
        import cv2
        import numpy as np

        # 创建临时视频文件
        temp_file = tempfile.NamedTemporaryFile(suffix='.mp4', delete=False)
        temp_file.close()

        # 视频参数
        width, height = 320, 240
        fps = 2
        duration = 3  # 秒

        # 创建视频写入器
        fourcc = cv2.VideoWriter_fourcc(*'mp4v')
        out = cv2.VideoWriter(temp_file.name, fourcc, fps, (width, height))

        # 生成简单的动画帧
        for frame_num in range(fps * duration):
            # 创建帧
            frame = np.zeros((height, width, 3), dtype=np.uint8)
            frame[:] = (50, 100, 150)  # 背景色

            # 绘制移动的圆
            center_x = int(width * (frame_num / (fps * duration)))
            center_y = height // 2
            cv2.circle(frame, (center_x, center_y), 20, (255, 255, 255), -1)

            # 添加文字
            cv2.putText(frame, f"Frame {frame_num + 1}", (10, 30),
                       cv2.FONT_HERSHEY_SIMPLEX, 0.7, (255, 255, 255), 2)

            out.write(frame)

        out.release()
        console.print("✅ 创建了测试视频")
        return temp_file.name

    except ImportError:
        # 如果 OpenCV 不可用，尝试使用现有的视频文件
        config = get_config()
        for video_path in config.assets_dir.rglob("*.mp4"):
            if video_path.exists():
                file_size = video_path.stat().st_size
                if file_size < 50 * 1024 * 1024:  # 小于 50MB
                    console.print(f"📁 使用现有视频: {video_path.name}")
                    return str(video_path)

        for video_path in config.assets_dir.rglob("*.mov"):
            if video_path.exists():
                file_size = video_path.stat().st_size
                if file_size < 50 * 1024 * 1024:  # 小于 50MB
                    console.print(f"📁 使用现有视频: {video_path.name}")
                    return str(video_path)

        console.print("⚠️ [yellow]无法创建或找到合适的测试视频[/yellow]")
        return None

    except Exception as e:
        console.print(f"⚠️ [yellow]创建测试视频失败: {e}[/yellow]")
        return None

def test_text_processing():
    """测试文字处理"""
    console.print("📝 [bold]测试文字处理...[/bold]")
    
    try:
        gemini_adapter = get_gemini_adapter()
        
        test_prompts = [
            "请用中文回答：什么是人工智能？",
            "Translate this to English: 你好，世界！",
            "解释一下机器学习的基本概念",
            "Generate a JSON object with keys 'name', 'age', 'city' for a fictional person"
        ]
        
        results = []
        
        with Progress(
            SpinnerColumn(),
            TextColumn("[progress.description]{task.description}"),
            console=console
        ) as progress:
            
            for i, prompt in enumerate(test_prompts):
                task = progress.add_task(f"测试提示 {i+1}/{len(test_prompts)}...", total=None)
                
                try:
                    response_text = gemini_adapter.generate_content(prompt)
                    if response_text:
                        results.append({
                            "prompt": prompt[:50] + "..." if len(prompt) > 50 else prompt,
                            "status": "✅ 成功",
                            "response_length": len(response_text),
                            "response_preview": response_text[:100] + "..." if len(response_text) > 100 else response_text
                        })
                    else:
                        results.append({
                            "prompt": prompt[:50] + "..." if len(prompt) > 50 else prompt,
                            "status": "❌ 无响应",
                            "response_length": 0,
                            "response_preview": ""
                        })
                        
                except Exception as e:
                    results.append({
                        "prompt": prompt[:50] + "..." if len(prompt) > 50 else prompt,
                        "status": f"❌ 错误: {str(e)[:30]}",
                        "response_length": 0,
                        "response_preview": ""
                    })
                
                progress.remove_task(task)
        
        # 显示结果
        table = Table(title="文字处理测试结果")
        table.add_column("提示", style="cyan", width=30)
        table.add_column("状态", style="magenta")
        table.add_column("响应长度", style="green")
        table.add_column("响应预览", style="yellow", width=40)
        
        for result in results:
            table.add_row(
                result["prompt"],
                result["status"],
                str(result["response_length"]),
                result["response_preview"]
            )
        
        console.print(table)
        
        success_count = sum(1 for r in results if "✅" in r["status"])
        console.print(f"\n📊 文字处理测试: {success_count}/{len(test_prompts)} 成功")
        
        return success_count == len(test_prompts)
        
    except Exception as e:
        console.print(f"❌ [red]文字处理测试失败: {e}[/red]")
        return False

def test_image_processing():
    """测试图片处理"""
    console.print("\n🖼️ [bold]测试图片处理...[/bold]")

    try:
        gemini_adapter = get_gemini_adapter()

        # 创建测试图片
        test_image_path = create_test_image()

        if not test_image_path:
            console.print("⚠️ [yellow]跳过图片测试（无法创建或找到测试图片）[/yellow]")
            return True  # 不算作失败

        try:
            with Progress(
                SpinnerColumn(),
                TextColumn("[progress.description]{task.description}"),
                console=console
            ) as progress:
                
                # 上传图片
                task = progress.add_task("上传测试图片...", total=None)
                uploaded_file = gemini_adapter.upload_file(test_image_path)

                progress.remove_task(task)

                if not uploaded_file:
                    console.print("❌ [red]图片上传失败[/red]")
                    return False
                
                # 测试不同的图片分析任务
                test_prompts = [
                    "描述这张图片的内容",
                    "这张图片中有什么颜色？",
                    "请用JSON格式分析这张图片，包含description、colors、objects等字段",
                    "这张图片适合用于什么类型的视频？"
                ]
                
                results = []
                
                for i, prompt in enumerate(test_prompts):
                    task = progress.add_task(f"图片分析 {i+1}/{len(test_prompts)}...", total=None)
                    
                    try:
                        response_text = gemini_adapter.generate_content([prompt, uploaded_file])
                        if response_text:
                            results.append({
                                "prompt": prompt,
                                "status": "✅ 成功",
                                "response": response_text[:200] + "..." if len(response_text) > 200 else response_text
                            })
                        else:
                            results.append({
                                "prompt": prompt,
                                "status": "❌ 无响应",
                                "response": ""
                            })
                            
                    except Exception as e:
                        results.append({
                            "prompt": prompt,
                            "status": f"❌ 错误: {str(e)[:30]}",
                            "response": ""
                        })
                    
                    progress.remove_task(task)
                
                # 清理上传的文件
                try:
                    gemini_adapter.delete_file(uploaded_file)
                except:
                    pass
            
            # 显示结果
            console.print("\n📋 图片分析结果:")
            for i, result in enumerate(results, 1):
                console.print(f"\n{i}. {result['prompt']}")
                console.print(f"   状态: {result['status']}")
                if result['response']:
                    console.print(f"   响应: {result['response']}")
            
            success_count = sum(1 for r in results if "✅" in r["status"])
            console.print(f"\n📊 图片处理测试: {success_count}/{len(test_prompts)} 成功")
            
            return success_count > 0
            
        finally:
            # 清理本地测试文件
            try:
                os.unlink(test_image_path)
            except:
                pass
        
    except Exception as e:
        console.print(f"❌ [red]图片处理测试失败: {e}[/red]")
        return False

def test_video_processing():
    """测试视频处理"""
    console.print("\n🎬 [bold]测试视频处理...[/bold]")
    
    # 创建测试视频
    test_video_path = create_test_video()
    
    if not test_video_path:
        console.print("⚠️ [yellow]跳过视频测试（无法创建测试视频）[/yellow]")
        return True  # 不算作失败
    
    try:
        gemini_adapter = get_gemini_adapter()
        
        try:
            with Progress(
                SpinnerColumn(),
                TextColumn("[progress.description]{task.description}"),
                console=console
            ) as progress:
                
                # 上传视频
                task = progress.add_task("上传测试视频...", total=None)
                uploaded_file = gemini_adapter.upload_file(test_video_path)

                progress.remove_task(task)

                if not uploaded_file:
                    console.print("❌ [red]视频上传失败[/red]")
                    return False
                
                # 测试视频分析
                test_prompts = [
                    "描述这个视频的内容",
                    "这个视频有多长？包含什么动作？",
                    "请用JSON格式分析这个视频，包含duration、content、movement等字段"
                ]
                
                results = []
                
                for i, prompt in enumerate(test_prompts):
                    task = progress.add_task(f"视频分析 {i+1}/{len(test_prompts)}...", total=None)
                    
                    try:
                        response_text = gemini_adapter.generate_content([prompt, uploaded_file])
                        if response_text:
                            results.append({
                                "prompt": prompt,
                                "status": "✅ 成功",
                                "response": response_text[:200] + "..." if len(response_text) > 200 else response_text
                            })
                        else:
                            results.append({
                                "prompt": prompt,
                                "status": "❌ 无响应",
                                "response": ""
                            })
                            
                    except Exception as e:
                        results.append({
                            "prompt": prompt,
                            "status": f"❌ 错误: {str(e)[:30]}",
                            "response": ""
                        })
                    
                    progress.remove_task(task)
                
                # 清理上传的文件
                try:
                    gemini_adapter.delete_file(uploaded_file)
                except:
                    pass
            
            # 显示结果
            console.print("\n📋 视频分析结果:")
            for i, result in enumerate(results, 1):
                console.print(f"\n{i}. {result['prompt']}")
                console.print(f"   状态: {result['status']}")
                if result['response']:
                    console.print(f"   响应: {result['response']}")
            
            success_count = sum(1 for r in results if "✅" in r["status"])
            console.print(f"\n📊 视频处理测试: {success_count}/{len(test_prompts)} 成功")
            
            return success_count > 0
            
        finally:
            # 清理本地测试文件
            try:
                os.unlink(test_video_path)
            except:
                pass
        
    except Exception as e:
        console.print(f"❌ [red]视频处理测试失败: {e}[/red]")
        return False

def main():
    """主测试函数"""
    console.print(Panel(
        "[bold blue]Gemini API 功能测试[/bold blue]\n" +
        "测试文字、图片和视频处理能力",
        border_style="blue"
    ))
    
    # 检查 API Key
    if not config.validate_api_key():
        console.print("❌ [red]API Key 未配置，请先配置 GEMINI_API_KEY[/red]")
        return False
    
    console.print(f"🔑 使用 API Key: {config.gemini_api_key[:10]}...{config.gemini_api_key[-4:]}")
    
    # 运行测试
    tests = [
        ("文字处理", test_text_processing),
        ("图片处理", test_image_processing),
        ("视频处理", test_video_processing)
    ]
    
    results = {}
    
    for test_name, test_func in tests:
        console.print(f"\n{'='*60}")
        console.print(f"🧪 [bold]开始测试: {test_name}[/bold]")
        console.print('='*60)
        
        try:
            results[test_name] = test_func()
        except Exception as e:
            console.print(f"❌ [red]{test_name} 测试异常: {e}[/red]")
            results[test_name] = False
    
    # 显示总结
    console.print(f"\n{'='*60}")
    console.print("📊 [bold]测试结果总结[/bold]")
    console.print('='*60)
    
    passed_tests = sum(results.values())
    total_tests = len(results)
    
    summary_table = Table(title="Gemini API 功能测试总结")
    summary_table.add_column("功能", style="cyan")
    summary_table.add_column("状态", style="magenta")
    
    for test_name, success in results.items():
        status = "✅ 通过" if success else "❌ 失败"
        summary_table.add_row(test_name, status)
    
    console.print(summary_table)
    
    if passed_tests == total_tests:
        console.print(f"\n🎉 [bold green]所有测试通过！({passed_tests}/{total_tests})[/bold green]")
        console.print("✨ [green]Gemini API 功能正常，可以正常使用 PyJianYin！[/green]")
    else:
        console.print(f"\n⚠️ [yellow]部分测试失败 ({passed_tests}/{total_tests})[/yellow]")
        console.print("🔧 [yellow]请检查网络连接和 API 配置[/yellow]")
    
    return passed_tests == total_tests

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
