import json
import os
import sys
from pathlib import Path
from typing import Optional, Dict, List
import librosa
import numpy as np
from rich.console import Console
from rich.progress import Progress

# 添加项目根目录到路径
sys.path.append(str(Path(__file__).parent.parent))
from config import get_config
from gemini_adapter import get_gemini_adapter

# 初始化配置和控制台
config = get_config()
console = Console()

# 初始化 Gemini 客户端
try:
    from google import genai
    from google.genai import types

    # 设置 API Key
    if not config.validate_api_key():
        console.print("❌ [red]错误: 请先配置 GEMINI_API_KEY 环境变量[/red]")
        sys.exit(1)

    import os
    os.environ['GOOGLE_API_KEY'] = config.gemini_api_key
    gemini_client = genai.Client()
    console.print("✅ [green]剧本生成器: 新版 Google GenAI SDK 初始化成功[/green]")

except ImportError:
    console.print("❌ [red]新版 Google GenAI SDK 未安装[/red]")
    console.print("💡 [yellow]请运行: pip install google-genai[/yellow]")
    sys.exit(1)
except Exception as e:
    console.print(f"❌ [red]剧本生成器: Gemini SDK 初始化失败: {e}[/red]")
    sys.exit(1)

def extract_assets_info(assets_json_path: str) -> Dict:
    """
    从 assets.json 提取素材信息

    Returns:
        dict: 包含 tags, categories, moods 等信息
    """
    try:
        with open(assets_json_path, 'r', encoding='utf-8') as f:
            assets = json.load(f)
    except Exception as e:
        console.print(f"❌ [red]无法读取素材文件: {e}[/red]")
        return {"tags": [], "categories": [], "moods": [], "assets": []}

    tags = set()
    categories = set()
    moods = set()

    for asset in assets:
        analysis = asset.get('analysis', {})

        # 收集标签
        for tag in analysis.get('tags', []):
            tags.add(tag)

        # 收集分类和情绪
        if analysis.get('category'):
            categories.add(analysis['category'])
        if analysis.get('mood'):
            moods.add(analysis['mood'])

    return {
        "tags": list(tags),
        "categories": list(categories),
        "moods": list(moods),
        "assets": assets
    }

def read_srt(srt_path: str) -> str:
    """读取 SRT 字幕文件内容"""
    try:
        with open(srt_path, 'r', encoding='utf-8') as f:
            return f.read()
    except Exception as e:
        console.print(f"❌ [red]无法读取字幕文件: {e}[/red]")
        return ""

def analyze_music_tempo(music_path: str) -> Dict:
    """
    分析音乐的节拍和节奏信息

    Returns:
        dict: 包含 BPM, 节拍点等信息
    """
    try:
        # 加载音频文件
        y, sr = librosa.load(music_path)

        # 分析节拍
        tempo, beats = librosa.beat.beat_track(y=y, sr=sr)

        # 计算节拍时间点
        beat_times = librosa.beat.beat_track(y=y, sr=sr, units='time')[1]

        # 分析音频特征
        spectral_centroids = librosa.feature.spectral_centroid(y=y, sr=sr)[0]
        spectral_rolloff = librosa.feature.spectral_rolloff(y=y, sr=sr)[0]

        # 判断音乐风格
        avg_centroid = np.mean(spectral_centroids)
        avg_rolloff = np.mean(spectral_rolloff)

        music_style = "moderate"
        if tempo > 140:
            music_style = "energetic"
        elif tempo < 80:
            music_style = "calm"

        return {
            "tempo": float(tempo),
            "beat_times": beat_times.tolist()[:50],  # 限制前50个节拍点
            "duration": len(y) / sr,
            "style": music_style,
            "avg_spectral_centroid": float(avg_centroid),
            "avg_spectral_rolloff": float(avg_rolloff)
        }

    except Exception as e:
        console.print(f"⚠️ [yellow]音乐分析失败: {e}[/yellow]")
        return {
            "tempo": 120.0,
            "beat_times": [],
            "duration": 60.0,
            "style": "moderate"
        }

def build_mode_specific_prompt(mode: str, theme: str, assets_info: Dict, srt_content: str, music_info: Dict) -> str:
    """根据视频模式构建专门的 Prompt"""

    mode_config = config.get_video_mode_config(mode)
    base_prompt = f"""
You are an expert video editor specializing in {mode} content. Create a structured storyboard in JSON format.

**Video Brief:**
- Theme: "{theme}"
- Mode: "{mode}" - {mode_config['description']}
- Available Asset Tags: {assets_info['tags']}
- Available Categories: {assets_info['categories']}
- Available Moods: {assets_info['moods']}
- Music Tempo: {music_info['tempo']} BPM
- Music Style: {music_info['style']}
- Music Duration: {music_info['duration']} seconds

**Subtitles Content:**
{srt_content}

**Scene Structure:**
Each scene should have:
- "type": "video" | "image" | "transition"
- "tags": [list of relevant tags from available assets]
- "duration_seconds": number (align with music tempo for rhythm)
- "comment": explanation of editing choice
- "timing": start time in seconds
- "transition": {{"name": "transition_name", "duration": 0.5}} (optional)

"""

    # 根据模式添加特定指令
    if mode == "知识科普":
        mode_prompt = """
**Knowledge Education Mode Instructions:**
1. Use clear, educational B-roll footage with tags like "technology", "science", "education"
2. Prefer "knowledge" category assets
3. Use calm transitions: "叠化", "淡入淡出", "闪白"
4. Scene duration: 3-8 seconds for good comprehension
5. Match visual content closely with subtitle meaning
6. Use charts/diagrams for complex concepts
7. Maintain professional, informative tone
"""

    elif mode == "经济解读":
        mode_prompt = """
**Economic Analysis Mode Instructions:**
1. Use business/finance related footage with tags like "economics", "business", "data"
2. Prefer "economics" category assets
3. Use professional transitions: "叠化", "推拉", "擦除"
4. Scene duration: 4-6 seconds for analysis depth
5. Include charts, graphs, and data visualizations
6. Maintain serious, analytical tone
7. Use "serious" or "professional" mood assets
"""

    elif mode == "搞笑卡点":
        mode_prompt = f"""
**Comedy Beat-Sync Mode Instructions:**
1. CRITICAL: Sync scenes with music beats at {music_info['tempo']} BPM
2. Use funny/meme content with tags like "funny", "meme", "entertainment"
3. Scene duration: 0.5-2 seconds (fast-paced, beat-synced)
4. Use dynamic transitions: "信号故障", "抖动", "闪烁", "缩放"
5. Beat timing points: {music_info['beat_times'][:10]}
6. Prefer "funny_moment" category and "humorous" mood
7. Create rhythm and energy matching music
"""

    elif mode == "主流视频二创":
        mode_prompt = """
**Mainstream Video Recreation Mode Instructions:**
1. Use highlight moments with tags like "dramatic", "key_moment", "highlight"
2. Scene duration: 2-5 seconds for impact
3. Use cinematic transitions: "叠化", "推拉", "旋转"
4. Focus on emotional peaks and key dialogues
5. Maintain narrative flow and dramatic tension
6. Use "inspirational" or "dramatic" mood assets
"""

    else:
        mode_prompt = """
**General Mode Instructions:**
1. Use relevant assets based on theme
2. Scene duration: 3-5 seconds
3. Use simple transitions: "叠化", "淡入淡出"
4. Match content with subtitles
"""

    return base_prompt + mode_prompt + """

**Output Format:**
Generate a JSON array of scenes. Example:
[
  {
    "type": "video",
    "tags": ["technology", "ai"],
    "duration_seconds": 4.5,
    "timing": 0,
    "comment": "Opening shot showing AI technology",
    "transition": {"name": "叠化", "duration": 0.5}
  },
  {
    "type": "image",
    "tags": ["chart", "data"],
    "duration_seconds": 3.0,
    "timing": 4.5,
    "comment": "Data visualization for statistics"
  }
]

Generate the complete storyboard now:
"""

def generate_storyboard(
    theme: str,
    mode: str,
    assets_json: str = None,
    srt_path: str = None,
    music_path: str = None,
    output_json: str = None
) -> bool:
    """
    生成视频剧本

    Args:
        theme: 视频主题
        mode: 视频模式
        assets_json: 素材数据库路径
        srt_path: 字幕文件路径
        music_path: 音乐文件路径
        output_json: 输出文件路径

    Returns:
        bool: 是否成功生成
    """
    # 使用默认路径
    if assets_json is None:
        assets_json = config.assets_json
    if output_json is None:
        output_json = config.storyboard_dir / "storyboard.json"

    console.print(f"🎬 [bold blue]生成剧本: {theme}[/bold blue]")
    console.print(f"📝 模式: {mode}")

    # 检查文件存在性
    if not Path(assets_json).exists():
        console.print(f"❌ [red]素材数据库不存在: {assets_json}[/red]")
        return False

    if srt_path and not Path(srt_path).exists():
        console.print(f"❌ [red]字幕文件不存在: {srt_path}[/red]")
        return False

    if music_path and not Path(music_path).exists():
        console.print(f"❌ [red]音乐文件不存在: {music_path}[/red]")
        return False

    try:
        with Progress() as progress:
            task = progress.add_task("生成剧本...", total=4)

            # 1. 提取素材信息
            progress.update(task, description="分析素材库...")
            assets_info = extract_assets_info(assets_json)
            progress.advance(task)

            # 2. 读取字幕
            progress.update(task, description="读取字幕...")
            srt_content = read_srt(srt_path) if srt_path else ""
            progress.advance(task)

            # 3. 分析音乐
            progress.update(task, description="分析音乐...")
            music_info = analyze_music_tempo(music_path) if music_path else {
                "tempo": 120.0, "beat_times": [], "duration": 60.0, "style": "moderate"
            }
            progress.advance(task)

            # 4. 生成剧本
            progress.update(task, description="AI 生成剧本...")
            prompt = build_mode_specific_prompt(mode, theme, assets_info, srt_content, music_info)

            response = gemini_client.models.generate_content(
                model=f'models/{config.gemini_model_flash}',
                contents=prompt
            )

            response_text = response.text if response and response.text else None

            if not response_text:
                console.print("❌ [red]AI 剧本生成失败[/red]")
                return False

            # 解析响应
            cleaned_text = response_text.strip()
            cleaned_text = cleaned_text.replace('```json', '').replace('```', '').strip()

            try:
                storyboard = json.loads(cleaned_text)
            except json.JSONDecodeError as e:
                console.print(f"❌ [red]JSON 解析失败: {e}[/red]")
                console.print(f"原始响应: {cleaned_text[:500]}...")
                return False

            # 保存结果
            output_path = Path(output_json)
            output_path.parent.mkdir(parents=True, exist_ok=True)

            with open(output_path, 'w', encoding='utf-8') as f:
                json.dump(storyboard, f, ensure_ascii=False, indent=2)

            progress.advance(task)

        console.print(f"✅ [green]剧本生成成功: {output_path}[/green]")
        console.print(f"📊 生成了 {len(storyboard)} 个场景")

        # 显示剧本摘要
        show_storyboard_summary(storyboard)
        return True

    except Exception as e:
        console.print(f"❌ [red]剧本生成失败: {e}[/red]")
        return False

def show_storyboard_summary(storyboard: List[Dict]):
    """显示剧本摘要"""
    if not storyboard:
        return

    total_duration = sum(scene.get("duration_seconds", 0) for scene in storyboard)
    video_scenes = sum(1 for scene in storyboard if scene.get("type") == "video")
    image_scenes = sum(1 for scene in storyboard if scene.get("type") == "image")
    transitions = sum(1 for scene in storyboard if scene.get("transition"))

    console.print(f"\n📋 [bold]剧本摘要:[/bold]")
    console.print(f"  • 总时长: {total_duration:.1f} 秒")
    console.print(f"  • 视频场景: {video_scenes}")
    console.print(f"  • 图片场景: {image_scenes}")
    console.print(f"  • 转场效果: {transitions}")

if __name__ == "__main__":
    console.print("🎬 [bold blue]PyJianYin 剧本生成器[/bold blue]")

    # 示例用法
    success = generate_storyboard(
        theme="人工智能对未来工作的影响",
        mode="知识科普",
        assets_json="../asset_manager/assets.json",
        srt_path="../examples/example.srt",
        music_path="../examples/example.mp3",
        output_json="storyboard.json"
    )

    if success:
        console.print("🎉 [green]剧本生成完成！[/green]")
    else:
        console.print("💥 [red]剧本生成失败[/red]")
        sys.exit(1)