[project]
name = "pyjianyin"
version = "0.1.0"
description = "基于 Gemini AI 和 pyJianYingDraft 的自动视频生成系统"
readme = "README.md"
requires-python = ">=3.10"
authors = [
    {name = "PyJianYin Team"}
]
keywords = ["video", "ai", "automation", "jianying", "gemini"]
classifiers = [
    "Development Status :: 3 - Alpha",
    "Intended Audience :: Developers",
    "License :: OSI Approved :: MIT License",
    "Programming Language :: Python :: 3",
    "Programming Language :: Python :: 3.10",
    "Programming Language :: Python :: 3.11",
    "Programming Language :: Python :: 3.12",
    "Topic :: Multimedia :: Video",
    "Topic :: Software Development :: Libraries :: Python Modules",
]
dependencies = [
    "google-genai>=1.0.0",
    "pymediainfo>=6.0.0",
    "pyJianYingDraft>=0.1.0",
    "librosa>=0.10.0",
    "click>=8.0.0",
    "rich>=13.0.0",
    "python-dotenv>=1.0.0",
    "requests>=2.25.0",
]

[project.optional-dependencies]
dev = [
    "pytest>=7.0.0",
    "pytest-cov>=4.0.0",
    "black>=23.0.0",
    "isort>=5.0.0",
    "flake8>=6.0.0",
]

[project.scripts]
pyjianyin = "main:cli"

[project.urls]
Homepage = "https://github.com/your-username/pyjianyin"
Repository = "https://github.com/your-username/pyjianyin"
Documentation = "https://github.com/your-username/pyjianyin/blob/main/README.md"
Issues = "https://github.com/your-username/pyjianyin/issues"

[build-system]
requires = ["hatchling"]
build-backend = "hatchling.build"

[tool.black]
line-length = 88
target-version = ['py310']

[tool.isort]
profile = "black"
line_length = 88

[tool.pytest.ini_options]
testpaths = ["tests"]
python_files = ["test_*.py"]
python_classes = ["Test*"]
python_functions = ["test_*"]
