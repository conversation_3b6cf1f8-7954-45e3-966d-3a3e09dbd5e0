#!/usr/bin/env python3
"""
Unicode 安全的测试脚本
专门处理 Windows 控制台的 Unicode 兼容性问题
"""

import os
import sys
import subprocess
from pathlib import Path

# 添加项目根目录到路径
sys.path.append(str(Path(__file__).parent))
from config import get_config
from console_utils import safe_console, print_success, print_error, print_warning, print_info, print_test

config = get_config()

def test_console_unicode():
    """测试控制台 Unicode 支持"""
    print_test("测试控制台 Unicode 支持")
    
    try:
        # 测试基本输出
        safe_console.print("[green]基本文本输出正常[/green]")
        
        # 测试 Unicode 字符
        try:
            safe_console.print("Unicode 测试: ✅ ❌ ⚠️ 💡 🔄 🧪")
            print_success("Unicode 字符支持正常")
            return True
        except UnicodeEncodeError:
            print_warning("Unicode 字符不支持，使用文本替代")
            return True
            
    except Exception as e:
        print_error(f"控制台测试失败: {e}")
        return False

def test_environment():
    """测试环境配置"""
    print_test("测试环境配置")
    
    try:
        # 检查 API Key
        if config.validate_api_key():
            print_success("API Key 已配置")
        else:
            print_error("API Key 未配置")
            return False
        
        # 检查素材目录
        if config.assets_dir.exists():
            print_success(f"素材目录存在: {config.assets_dir}")
        else:
            print_warning(f"素材目录不存在，创建: {config.assets_dir}")
            config.assets_dir.mkdir(parents=True, exist_ok=True)
            print_success("素材目录创建成功")
        
        return True
        
    except Exception as e:
        print_error(f"环境测试失败: {e}")
        return False

def test_ffmpeg():
    """测试 FFmpeg"""
    print_test("测试 FFmpeg")
    
    try:
        result = subprocess.run(['ffmpeg', '-version'], 
                              capture_output=True, 
                              timeout=10)
        
        if result.returncode == 0:
            print_success("FFmpeg 可用")
            return True
        else:
            print_error("FFmpeg 不可用")
            return False
            
    except FileNotFoundError:
        print_error("FFmpeg 未安装")
        return False
    except subprocess.TimeoutExpired:
        print_error("FFmpeg 测试超时")
        return False
    except Exception as e:
        print_error(f"FFmpeg 测试失败: {e}")
        return False

def test_gemini_sdk():
    """测试 Gemini SDK"""
    print_test("测试 Gemini SDK")
    
    try:
        from google import genai
        from google.genai import types
        print_success("Google GenAI SDK 导入成功")
        
        # 设置 API Key
        os.environ['GOOGLE_API_KEY'] = config.gemini_api_key
        client = genai.Client()
        print_success("Gemini 客户端创建成功")
        
        # 简单测试
        response = client.models.generate_content(
            model='models/gemini-2.5-flash',
            contents="Hello, please respond with 'Test OK'."
        )
        
        if response and response.text and "OK" in response.text:
            print_success("Gemini API 调用成功")
            return True
        else:
            print_warning("Gemini API 响应异常")
            return False
            
    except ImportError:
        print_error("Google GenAI SDK 未安装")
        return False
    except Exception as e:
        print_error(f"Gemini SDK 测试失败: {e}")
        return False

def test_file_operations():
    """测试文件操作"""
    print_test("测试文件操作")
    
    try:
        # 扫描素材目录
        file_count = 0
        large_video_count = 0
        
        if config.assets_dir.exists():
            for file_path in config.assets_dir.rglob("*"):
                if file_path.is_file():
                    file_count += 1
                    
                    # 检查是否为大视频
                    if file_path.suffix.lower() in {'.mp4', '.mov', '.avi', '.mkv'}:
                        file_size = file_path.stat().st_size / 1024 / 1024
                        if file_size > 20:
                            large_video_count += 1
        
        print_success(f"扫描到 {file_count} 个文件")
        if large_video_count > 0:
            print_info(f"发现 {large_video_count} 个大视频文件 (>20MB)")
        
        return True
        
    except Exception as e:
        print_error(f"文件操作测试失败: {e}")
        return False

def run_subprocess_test(script_name: str, description: str):
    """运行子进程测试"""
    print_test(f"测试 {description}")

    try:
        # 设置环境变量以确保 Unicode 支持
        env = os.environ.copy()
        if sys.platform == "win32":
            env['PYTHONIOENCODING'] = 'utf-8'
            env['PYTHONUTF8'] = '1'

        result = subprocess.run(
            [sys.executable, script_name],
            capture_output=True,
            text=True,
            timeout=60,
            cwd=config.project_root,
            encoding='utf-8',
            errors='replace',  # 处理编码错误
            env=env
        )

        if result.returncode == 0:
            print_success(f"{description} 测试成功")
            return True
        else:
            print_error(f"{description} 测试失败")
            # 显示错误信息（安全处理）
            if result.stderr:
                error_msg = result.stderr[:500].replace('\n', ' ').replace('\r', ' ')
                # 进一步清理特殊字符
                error_msg = ''.join(char if ord(char) < 128 or char.isalnum() else ' ' for char in error_msg)
                safe_console.print(f"[red]错误:[/red] {error_msg}")
            if result.stdout:
                # 也显示标准输出的最后部分，可能包含有用信息
                stdout_msg = result.stdout[-300:].replace('\n', ' ').replace('\r', ' ')
                stdout_msg = ''.join(char if ord(char) < 128 or char.isalnum() else ' ' for char in stdout_msg)
                safe_console.print(f"[yellow]输出:[/yellow] ...{stdout_msg}")
            return False

    except subprocess.TimeoutExpired:
        print_error(f"{description} 测试超时")
        return False
    except Exception as e:
        print_error(f"{description} 测试异常: {e}")
        return False

def main():
    """主测试函数"""
    safe_console.print("[bold blue]PyJianYin Unicode 安全测试[/bold blue]")
    safe_console.print("解决 Windows 控制台 Unicode 兼容性问题")
    safe_console.print("=" * 60)
    
    # 基础测试
    basic_tests = [
        ("控制台 Unicode", test_console_unicode),
        ("环境配置", test_environment),
        ("FFmpeg", test_ffmpeg),
        ("Gemini SDK", test_gemini_sdk),
        ("文件操作", test_file_operations)
    ]
    
    results = {}
    
    for test_name, test_func in basic_tests:
        safe_console.print(f"\n[cyan]--- {test_name} ---[/cyan]")
        try:
            results[test_name] = test_func()
        except Exception as e:
            print_error(f"{test_name} 测试异常: {e}")
            results[test_name] = False
    
    # 子进程测试（如果基础测试通过）
    basic_passed = sum(results.values())
    if basic_passed >= 4:  # 至少4个基础测试通过
        subprocess_tests = [
            ("check_file_sizes.py", "文件大小检查"),
            ("test_new_api.py", "新版 API"),
        ]
        
        for script, description in subprocess_tests:
            safe_console.print(f"\n[cyan]--- {description} ---[/cyan]")
            results[description] = run_subprocess_test(script, description)
    
    # 显示结果
    safe_console.print("\n" + "=" * 60)
    safe_console.print("[bold]测试结果总结[/bold]")
    safe_console.print("=" * 60)
    
    passed_tests = sum(results.values())
    total_tests = len(results)
    
    for test_name, success in results.items():
        status = "PASS" if success else "FAIL"
        style = "green" if success else "red"
        safe_console.print(f"[{style}]{status}[/{style}]: {test_name}")
    
    # 总结
    if passed_tests == total_tests:
        print_success(f"所有测试通过! ({passed_tests}/{total_tests})")
        safe_console.print("\n[bold green]PyJianYin 功能正常，可以开始使用![/bold green]")
        
        safe_console.print("\n[bold]建议的下一步:[/bold]")
        safe_console.print("1. 添加素材文件到 my_video_assets 目录")
        safe_console.print("2. 运行 'python main.py analyze' 分析素材")
        safe_console.print("3. 如有大视频文件，系统会自动处理")
        
    else:
        print_warning(f"部分测试失败 ({passed_tests}/{total_tests})")
        
        failed_tests = [name for name, success in results.items() if not success]
        safe_console.print("\n[bold red]失败的测试:[/bold red]")
        for test_name in failed_tests:
            safe_console.print(f"- {test_name}")
        
        safe_console.print("\n[bold]修复建议:[/bold]")
        if "Gemini SDK" in failed_tests:
            safe_console.print("- 检查网络连接和 API Key")
        if "FFmpeg" in failed_tests:
            safe_console.print("- 安装 FFmpeg: https://ffmpeg.org/download.html")
        if "环境配置" in failed_tests:
            safe_console.print("- 检查文件权限和磁盘空间")
    
    return passed_tests == total_tests

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
